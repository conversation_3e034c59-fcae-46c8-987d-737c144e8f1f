import React, {useCallback, useEffect, useState} from 'react';
import {FlatList, Pressable, StyleSheet, Text, View} from 'react-native';
import {useSelector} from 'react-redux';
import _ from 'lodash';

import {
  generateTypographyByPlatform,
  useTheme,
  useLoadedFonts,
  getShadowStyle,
  GetRegisteredPluginInfo,
  datasourceTypeModelSel
} from 'apptile-core';

import {registerNativePage} from '../../prebuilt';
import {ImageComponent} from 'apptile-core';
import {processShopifyGraphqlQueryResponse} from 'apptile-core';

const ITEM_HEIGHT = 290;
const ITEM_WIDTH = 150;
const ITEM_VMARGIN = 8;

const shopifyQueryExecutor = async (model: any, inputVariables: any) => {
  const queryRunner = model.get('queryRunner');
  const shopConfig = model.get('shop');

  const shopifyDatasouceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
  const queries = shopifyDatasouceConfig?.plugin?.getQueries();
  const querySchema = queries?.GetCollectionProductsById;

  const input = querySchema.inputResolver ? querySchema.inputResolver(inputVariables) : inputVariables;
  const response = await queryRunner.runQuery(querySchema.queryType, querySchema.gqlTag, input, {});
  const {transformedData, transformedError} = processShopifyGraphqlQueryResponse(response, querySchema, shopConfig);
  return {transformedData, transformedError};
};

const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');

const JolenePLPCollectionCarousel: React.FC = props => {
  const {collectionId, onProductClick, onAddToCart, onAddToWishlist, onRemoveFromWishlist} = props as any;
  const ShopifyDSModel = useSelector(shopifyModelSel);

  const [collectionProducts, setCollectionProducts] = useState([]);
  const [collectionDetails, setCollectionDetails] = useState(false);

  useEffect(() => {
    const fetchCollDetails = async () => {
      const {transformedData, transformedError} = await shopifyQueryExecutor(ShopifyDSModel, {
        id: collectionId,
        collectionMetafields: [],
        productMetafields: [],
        variantMetafields: [],
        first: 5,
        sortKey: 'BEST_SELLING',
        reverse: false,
      });
      // navigation.setOptions({title: transformedData?.title});
      // transformedData
      setCollectionProducts(transformedData);
    };
    fetchCollDetails();
    return () => {};
  }, [ShopifyDSModel, collectionId]);

  const renderItem = useCallback(
    ({item, index}) => {
      // logger.info(`ListView RenderItem ${index}`);
      return (
        <ItemComponent
          {...{
            item,
            index,
            onProductClick,
            onAddToCart,
            onAddToWishlist,
            onRemoveFromWishlist,
          }}
        />
      );
    },
    [onProductClick, onAddToCart, onAddToWishlist, onRemoveFromWishlist],
  );

  return (
    <View style={[fixedStyles.verticalSpacing]}>
      <FlatList
        data={collectionProducts || []}
        renderItem={renderItem}
        horizontal
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

const ItemComponent = React.memo(
  ({item, index, onProductClick, onAddToCart, onAddToWishlist, onRemoveFromWishlist}) => {
    const {themeEvaluator} = useTheme();
    const {loadedFonts} = useLoadedFonts();
    const headingStyles = generateTypographyByPlatform(themeEvaluator('tile.text.heading.typography'), loadedFonts);
    const bodyStyles = generateTypographyByPlatform(themeEvaluator('typography.body'), loadedFonts);
    const buttonTheme = themeEvaluator('tile.button.primary');
    const outlineButtonTheme = themeEvaluator('tile.button.outline');
    const textColor = themeEvaluator('colors.onBackground');
    const shadowStyles = _.merge({shadowColor: '#000'}, getShadowStyle(10));
    const {
      typography: buttonTypo,
      color,
      disabledColor,
      disabledBackgroundColor,
      backgroundColor,
      ...buttonStyles
    } = buttonTheme;
    const outlinebuttonStyles = _.omit(outlineButtonTheme, [
      'typography',
      'color',
      'disabledColor',
      'disabledBackgroundColor',
      'backgroundColor',
    ]);
    const buttonTextStyles = generateTypographyByPlatform(buttonTypo, loadedFonts);
    const [inWL, setInWL] = useState(false);
    return (
      <View
        style={[
          {
            width: ITEM_WIDTH,
            maxWidth: ITEM_WIDTH,
            height: ITEM_HEIGHT,
            overflow: 'hidden',
            marginVertical: ITEM_VMARGIN,
            paddingRight: 10,
            backgroundColor: 'white',
          },
        ]}>
        <Pressable style={[{flex: 1}]} onPress={() => onProductClick(item?.handle)}>
          <ImageComponent
            style={[{width: '100%', aspectRatio: 0.6}]}
            source={{
              uri: item?.featuredImage,
            }}
            resizeMode="cover"
          />
          <View style={[fixedStyles.detailsContainer]}>
            <Text
              style={[bodyStyles, {color: textColor, fontSize: 14, lineHeight: 18}]}
              numberOfLines={1}
              minimumFontScale={1}>
              {item?.title}
            </Text>
            <View style={[fixedStyles.priceContainer]}>
              <Text style={[bodyStyles, fixedStyles.mainPrice]}>{item?.displayMinSalePrice}</Text>
            </View>
          </View>
        </Pressable>
        {/* <View style={[fixedStyles.ctaActionsContainer]}>
          {item?.availableForSale ? (
            <Pressable
              style={[buttonStyles, fixedStyles.ctaButton, {backgroundColor}]}
              onPress={() => {
                onAddToCart(item?.variants[0]?.id);
              }}>
              <Text style={[buttonTextStyles, {color}]}>Add to Cart</Text>
            </Pressable>
          ) : (
            <Pressable style={[outlinebuttonStyles, fixedStyles.ctaButton]}>
              <Text style={[buttonTextStyles, {color: '#333'}]}>Out of stock</Text>
            </Pressable>
          )}
          <Pressable
            style={[fixedStyles.wishlistButton]}
            onPress={() => {
              inWL ? onRemoveFromWishlist(item?.id) : onAddToWishlist(item);
              setInWL(!inWL);
            }}>
            <MaterialCommunityIcons
              size={24}
              name={inWL ? 'heart' : 'heart-outline'}
              color={inWL ? '#e65a4c' : '#333'}
            />
          </Pressable>
        </View> */}
      </View>
    );
  },
);

const fixedStyles = StyleSheet.create({
  verticalSpacing: {marginTop: 4, marginBottom: 0},
  detailsContainer: {
    flex: 1,
    marginTop: 8,
  },
  ratingContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingStyle: {
    fontSize: 13,
    lineHeight: 18,
    color: '#000',
  },
  ratingStarStyle: {
    fontSize: 13,
    lineHeight: 18,
    color: '#50ac0a',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 4,
  },
  mainPrice: {
    fontSize: 13,
    lineHeight: 18,
    color: '#000',
  },
  strikeOffPrice: {
    fontSize: 10,
    marginHorizontal: 4,
  },
  ctaActionsContainer: {
    flex: 0,
    flexDirection: 'row',
    flexBasis: 'auto',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  ctaButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  wishlistButton: {
    margin: 4,
    flex: 0,
    justifyContent: 'center',
    alignItems: 'center',
    flexBasis: 'auto',
  },
});

export default registerNativePage(
  'JolenePLPCollectionCarousal',
  JolenePLPCollectionCarousel,
  {},
  {},
);
