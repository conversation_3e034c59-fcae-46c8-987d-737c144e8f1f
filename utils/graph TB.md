graph TB
    %% Frontend Entry Points
    subgraph "Frontend Entry Points"
        A[User Types Prompt] --> B{User Context}
        B -->|Logged Out| C[PromptToApp Component]
        B -->|Logged In - Studio| D[PrompterInput Component]
        B -->|Logged In - Chat| E[AIClientV2 Component]
    end

    %% Frontend Processing
    subgraph "Frontend Processing"
        C --> F[createNewApp Function]
        D --> G[handleSend Function]
        E --> H[handleSendMessage Function]
        
        F --> I[App Name Generation API]
        F --> J[App Creation API]
        F --> K[App Cloning Process]
        
        G --> L[runChatCompletionRequest]
        H --> L
    end

    %% API Routing Layer
    subgraph "API Routing Layer"
        I --> M["/api/generate-app-name"]
        J --> N["/api/orgs/{orgId}/app"]
        K --> O["/cli/workspaces/{id}/clone-open"]
        L --> P{Routing Decision}
        
        P -->|usePlanner=true| Q["/plugins/prompt/{appId}/provider/{provider}/model/{model}"]
        P -->|usePlanner=false| Q
        P -->|App Level| R["/{appId}/prompt/provider/{provider}/model/{model}"]
    end

    %% Agent Selection Logic
    subgraph "Agent Selection Logic"
        Q --> S{usePlanner Flag?}
        R --> T[AppPlannerAgent]
        
        S -->|true| T
        S -->|false| U[PluginAgent]
    end

    %% Agent Architecture
    subgraph "Agent Architecture"
        subgraph "BaseAgent Foundation"
            V[BaseAgent Class]
            V --> W[AI Model Clients]
            W --> W1[Claude/Anthropic]
            W --> W2[OpenAI/GPT]
            W --> W3[Google/Gemini]
            W --> W4[Amazon/Bedrock]
            
            V --> X[MCP Client Connection]
            V --> Y[Token Management]
            V --> Z[Chat History Management]
        end
        
        subgraph "AppPlannerAgent"
            T --> AA[App-Level Planning]
            AA --> AB[System Prompt: appPlanner.html]
            AA --> AC[Tool Groups: Planner Tools]
        end
        
        subgraph "PluginAgent"
            U --> AD[Plugin-Level Development]
            AD --> AE[System Prompt: latest.html]
            AD --> AF[Tool Groups: Plugin Tools]
        end
    end

    %% MCP Server Architecture
    subgraph "MCP Server Architecture"
        X --> AG[MCP Server Process]
        AG --> AH[Tool Group Loading]
        
        AH --> AI[graphqlSchema Tools]
        AH --> AJ[projectCodeRead Tools]
        AH --> AK[projectCodeWrite Tools]
        AH --> AL[codeExecution Tools]
        AH --> AM[dashboardRead Tools]
        AH --> AN[dashboardWrite Tools]
        AH --> AO[supabaseTools]
        AH --> AP[supabaseReadTools]
        AH --> AQ[promptPluginAgent Tools]
        AH --> AR[batchedTool Tools]
    end

    %% Tool Categories Detail
    subgraph "Tool Categories"
        subgraph "File Operations"
            AK --> AS[apply_file_ops]
            AJ --> AT[read_plugin_files]
            AJ --> AU[get_hunk_from_file]
        end
        
        subgraph "Supabase Integration"
            AO --> AV[supabase_run_sql_query]
            AO --> AW[supabase_create_table]
            AP --> AX[supabase_api_creds]
            AP --> AY[list_tables_with_realtime]
        end
        
        subgraph "Platform Operations"
            AN --> AZ[nocodelayer_create_plugin]
            AN --> BA[nocodelayer_create_screen]
            AN --> BB[nocodelayer_add_plugin_to_screen]
            AQ --> BC[nocodelayer_generate_code_for_plugin]
        end
        
        subgraph "Code Execution"
            AL --> BD[compile_generated_code]
            AL --> BE[run_curl]
            AL --> BF[lookup_images_from_pexels]
        end
        
        subgraph "External Integrations"
            BG[HubSpot Integration]
            BH[In-App Purchase Integration]
            BI[Custom MCP Servers]
        end
    end

    %% AI Model Processing
    subgraph "AI Model Processing"
        W1 --> BJ[Claude API Call]
        W2 --> BK[OpenAI API Call]
        W3 --> BL[Google API Call]
        W4 --> BM[Amazon Bedrock Call]
        
        BJ --> BN[Streaming Response]
        BK --> BN
        BL --> BN
        BM --> BN
    end

    %% Tool Execution Flow
    subgraph "Tool Execution Flow"
        BN --> BO{Tool Calls Detected?}
        BO -->|Yes| BP[Tool Execution Loop]
        BO -->|No| BQ[Stream Text Response]
        
        BP --> BR[Load Apptile Context]
        BR --> BS[Execute Tool via MCP]
        BS --> BT[Tool Result Processing]
        BT --> BU{Continue Chat?}
        BU -->|Yes| BJ
        BU -->|No| BV[Final Response]
    end

    %% File System Operations
    subgraph "File System Operations"
        AS --> BW[Create/Update JSX Files]
        BW --> BX[component.jsx Generation]
        BW --> BY[widget.jsx Auto-generation]
        BW --> BZ[File Structure Management]
        
        BZ --> CA["{appId}/remoteCode/plugins/{pluginName}/"]
        CA --> CB["source/component.jsx"]
        CA --> CC["source/widget.jsx"]
        CA --> CD["dist/"]
        CA --> CE["metadata.json"]
    end

    %% Database Operations
    subgraph "Database Operations"
        Z --> CF[Chat Management]
        CF --> CG[Create/Retrieve Chat Session]
        CF --> CH[Persist Chat Messages]
        CF --> CI[Sequence Tool Calls]
        
        AV --> CJ[Supabase SQL Execution]
        CJ --> CK[Database Schema Operations]
        CJ --> CL[Real-time Subscriptions]
    end

    %% Response Streaming
    subgraph "Response Streaming"
        BQ --> CM[HTTP Streaming Response]
        BV --> CM
        CM --> CN[Frontend Stream Processing]
        CN --> CO[Live Message Updates]
        CN --> CP[Chat History Updates]
        CN --> CQ[UI State Updates]
    end

    %% Integration Flows
    subgraph "Integration Flows"
        BG --> CR[HubSpot API Calls]
        CR --> CS[Contact Management]
        CR --> CT[Deal Tracking]
        
        BH --> CU[App Store Connect]
        CU --> CV[Subscription Management]
        CU --> CW[Purchase Validation]
        
        BI --> CX[Custom Tool Implementation]
        CX --> CY[External API Integration]
    end

    %% Compilation and Deployment
    subgraph "Compilation & Deployment"
        BD --> CZ[Plugin Compilation]
        CZ --> DA[Bundle Generation]
        DA --> DB[Error Checking]
        DB --> DC[Hot Reload Trigger]
    end

    %% Styling
    classDef frontend fill:#e1f5fe
    classDef api fill:#f3e5f5
    classDef agent fill:#e8f5e8
    classDef mcp fill:#fff3e0
    classDef tool fill:#fce4ec
    classDef ai fill:#e0f2f1
    classDef file fill:#f1f8e9
    classDef db fill:#e3f2fd
    classDef stream fill:#fafafa
    
    class A,B,C,D,E,F,G,H frontend
    class I,J,K,L,M,N,O,P,Q,R api
    class S,T,U,V,W,X,Y,Z,AA,AB,AC,AD,AE,AF agent
    class AG,AH,AI,AJ,AK,AL,AM,AN,AO,AP,AQ,AR mcp
    class AS,AT,AU,AV,AW,AX,AY,AZ,BA,BB,BC,BD,BE,BF,BG,BH,BI tool
    class W1,W2,W3,W4,BJ,BK,BL,BM,BN,BO,BP,BR,BS,BT,BU,BV ai
    class BW,BX,BY,BZ,CA,CB,CC,CD,CE file
    class CF,CG,CH,CI,CJ,CK,CL db
    class CM,CN,CO,CP,CQ stream