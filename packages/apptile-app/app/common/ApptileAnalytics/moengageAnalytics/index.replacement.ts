import _ from 'lodash';
import ReactMoE, {MoEProperties} from 'react-native-moengage';

const prevEvent: [any, any] = [null, null];

export const sendToMoEngage = (eventName: string, eventData: Record<string, any>) => {
  if (!eventName) return;
  const _eventData = eventData?.toJS ? eventData.toJS() : eventData;
  if (_.isEqual(prevEvent, [eventName, _eventData])) return;
  prevEvent[0] = eventName;
  prevEvent[1] = _eventData;

  logger.info('sendToMoEngage', eventName, _eventData);

  // auth
  // ! Template event name standardize case handling for pilgrim will be removed in next build
  if (eventName === ('customerLogIn' || 'login') || eventName === ('customerRegistered' || 'signup')) {
    ReactMoE.setUserUniqueID(_eventData?.emailId);
    // ReactMoE.setUserName('abc');
    ReactMoE.setUserFirstName(_eventData?.firstName);
    ReactMoE.setUserLastName(_eventData?.lastName);
    ReactMoE.setUserEmailID(_eventData?.emailId);
    ReactMoE.setUserContactNumber(replacePlusSymbolFromPhoneNumber(_eventData?.contactNumber));
    // ReactMoE.setUserGender('Male'); // OR Female
    // ReactMoE.setUserBirthday('1970-01-01T12:00:00Z'); // Format - ISO-8601 String
    // ReactMoE.setUserLocation(new MoEGeoLocation(77.3201, -77.3201)); // For Location use MoEGeoLocation instance

    let properties = new MoEProperties();
    properties.addAttribute('userUniqueID', _eventData?.emailId);
    properties.addAttribute('userFirstName', _eventData?.firstName);
    properties.addAttribute('userLastName', _eventData?.lastName);
    properties.addAttribute('userEmailID', _eventData?.emailId);
    properties.addAttribute('userContactNumber', replacePlusSymbolFromPhoneNumber(_eventData?.contactNumber));
    ReactMoE.trackEvent(
      eventName === ('customerRegistered' || 'signup') ? 'Customer Registered' : 'Customer Logged In',
      properties,
    );
  }
  // ! Template standardize case handling for pilgrim will be removed in next build
  if (eventName === ('customerLogout' || 'logout')) {
    ReactMoE.logout();
  }

  // pageView
  if (eventName === 'pageView') {
    if (_eventData?.pageName === 'Collection') {
      let properties = new MoEProperties();
      properties.addAttribute('collectionHandle', _eventData?.collectionHandle);
      ReactMoE.trackEvent('Collection Viewed', properties);
    } else if (_eventData?.pageName === 'Product') {
      let properties = new MoEProperties();
      properties.addAttribute('productHandle', _eventData?.productHandle);
      properties.addAttribute('productName', _eventData?.productName);
      ReactMoE.trackEvent('Product Viewed', properties);
    } else {
      let properties = new MoEProperties();
      properties.addAttribute('pageName', _eventData?.pageName);
      ReactMoE.trackEvent('Page Viewed', properties);
    }
  }

  // events
  if (eventName === 'addToWishlist') {
    let properties = new MoEProperties();
    properties.addAttribute('productHandle', _eventData?.productHandle);
    properties.addAttribute('productId', _eventData?.productId);
    ReactMoE.trackEvent('Added To Wishlist', properties);
  }
  if (eventName === 'removeFromWishlist') {
    let properties = new MoEProperties();
    properties.addAttribute('productHandle', _eventData?.productHandle);
    properties.addAttribute('productId', _eventData?.productId);
    ReactMoE.trackEvent('Removed From Wishlist', properties);
  }
  if (eventName === 'search') {
    let properties = new MoEProperties();
    properties.addAttribute('query', _eventData?.query);
    properties.addAttribute('selection', _eventData?.selection);
    ReactMoE.trackEvent('Product Searched', properties);
  }
  if (eventName === 'addToCart') {
    let properties = new MoEProperties();
    Object.keys(_eventData).forEach(k => properties.addAttribute(k, _eventData[k]));
    ReactMoE.trackEvent('Add To Cart', properties);
  }
  if (eventName === 'removeFromCart') {
    let properties = new MoEProperties();
    Object.keys(_eventData).forEach(k => properties.addAttribute(k, _eventData[k]));
    ReactMoE.trackEvent('Removed from Cart', properties);
  }
  if (eventName === 'updateCart') {
    let properties = new MoEProperties();
    // expected keys: action: 'increased_quantity'|'decreased_quantity'|'removed', productHandle, productId
    Object.keys(_eventData).forEach(k => properties.addAttribute(k, _eventData[k]));
    ReactMoE.trackEvent('Update Cart', properties);
  }
  if (eventName === 'checkout') {
    let properties = new MoEProperties();
    Object.keys(_eventData).forEach(k => properties.addAttribute(k, _eventData[k]));
    ReactMoE.trackEvent('Checkout Started', properties);
  }
  if (eventName === 'purchase') {
    let properties = new MoEProperties();
    properties.addAttribute('orderId', _eventData?.orderId);
    properties.addAttribute('totalValue', _eventData?.totalValue);
    properties.addAttribute('shippingDiscount', _eventData?.shippingDiscount);
    properties.addAttribute('shippingAmount', _eventData?.shippingAmount);
    properties.addAttribute('currency', _eventData?.currency);
    properties.addAttribute('totalItems', _eventData?.totalItems);
    properties.addAttribute('taxPrice', _eventData?.taxPrice);
    // properties.addDateAttribute('purchase_date', '2020-06-10T12:42:10Z');
    // properties.addLocationAttribute('store_location', new MoEGeoLocation(90.00001, 180.00001));
    properties.setNonInteractiveEvent();
    ReactMoE.trackEvent('Order Success', properties);

    (_eventData?.items ?? []).forEach((i: any) => {
      let _properties = new MoEProperties();
      _properties.addAttribute('orderId', i?.orderId);
      _properties.addAttribute('title', i?.title);
      _properties.addAttribute('currency', i?.currency);
      _properties.addAttribute('variantTitle', i?.variantTitle);
      _properties.addAttribute('price', i?.price);
      _properties.addAttribute('quantity', i?.quantity);
      _properties.addAttribute('variantId', i?.variantId);
      _properties.addAttribute('productId', i?.productId);
      _properties.addAttribute('productType', i?.productType);
      _properties.addAttribute('discount', i?.discount);
      properties.setNonInteractiveEvent();
      ReactMoE.trackEvent('Item Purchased', _properties);
    });
  }
};

const replacePlusSymbolFromPhoneNumber = (phoneNumber: string) => {
  return phoneNumber?.replace('+', '');
};