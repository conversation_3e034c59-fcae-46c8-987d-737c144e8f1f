import {Dimensions, InteractionManager, Platform} from 'react-native';
import {
  getApplicationName,
  getBuildNumber,
  getCarrier,
  getDeviceName,
  getInstanceId,
  getManufacturer,
  getModel,
  getSystemName,
  getSystemVersion,
  getVersion,
} from 'react-native-device-info';

import RNGetValues from '../../lib/RNGetValues';
import {apptileStateSelector} from 'apptile-core';
import {ApptileLocalSettings} from 'apptile-core';
import {getUniqueDeviceId} from 'apptile-core';
import {ApptileAnalyticsEventType, IApptileAnalyticsEvent} from 'apptile-core';
import {Firebase} from './firebaseAnalytics';
import {Facebook} from './facebookAnalytics';
import {sendToMoEngage} from './moengageAnalytics';
import {sendToAppsflyer} from './appsflyerAnalytics';
import {sendToCleverTap} from './cleverTapAnalytics';
import {sendToOneSignal} from './onesignalAnalytics';
import {sendToKlaviyo} from './klaviyoAnalytics';
import {Segment} from './segmentAnalytics';
import {store as storeModule} from 'apptile-core';

type MarkerInfo = {start: number; end: number; meta?: any;};
export default class ApptileAnalytics {
  private static _perfMarkerBuffer = new Map<string, MarkerInfo>();

  private static _context: IApptileAnalyticsEvent['context'];
  private static _eventsBuffer: Array<IApptileAnalyticsEvent> = [];
  private static store;

  static markStart(label: string, meta?: any) {
    const payload: MarkerInfo = {start: Date.now(), end: 0};
    if (meta) {
      payload.meta = meta;
    }
    ApptileAnalytics._perfMarkerBuffer.set(label, payload);
  }

  static markEnd(label: string, meta?: any) {
    const measure = ApptileAnalytics._perfMarkerBuffer.get(label);
    if (measure) {
      measure.end = Date.now();
      if (meta && measure.meta) {
        measure.meta.end = meta;
      } else if (meta) {
        measure.meta = meta;
      }
    } else {
      logger.error("[PERF] tried to end marker that doesn't exist " + label);
    }
    ApptileAnalytics.flushPerfEvents();
  }

  private static flushPerfEvents() {
    if (Segment.initialized && ApptileAnalytics._perfMarkerBuffer.size > 0) {
      for (let label of ApptileAnalytics._perfMarkerBuffer.keys()) {
        const measure = ApptileAnalytics._perfMarkerBuffer.get(label);
        if (measure && measure.end > 0) {
          const delta = measure.end - measure.start;
          logger.info('[PERF] flushing: ', label, delta, measure.meta);
          Segment.sendEvent('track', label, Object.assign({delta}, measure.meta));
          ApptileAnalytics._perfMarkerBuffer.delete(label);
        }
      }
    }
  }

  static initialize = async () => {
    try {
      if (ApptileAnalytics._context != null) {
        return;
      }
      ApptileAnalytics.store = storeModule;
      const deviceInstanceId = await getInstanceId();
      const deviceManufacturer = await getManufacturer();
      const deviceName = await getDeviceName();
      const carrierName = await getCarrier();
      const screenDimensions = Dimensions.get('screen');

      const firebaseAppInstanceID = await Firebase.getAppInstanceId();
      await Firebase.setUser(ApptileLocalSettings.userIdentifier);
      await Facebook.prepareSdk();
      Segment.setUser(ApptileLocalSettings.userIdentifier);

      ApptileAnalytics._context = {
        app: {
          name: getApplicationName(),
          version: getVersion(),
          build: getBuildNumber(),
        },
        device: {
          type: Platform.OS,
          id: getUniqueDeviceId(),
          advertisingId: deviceInstanceId,
          manufacturer: deviceManufacturer,
          model: getModel(),
          name: deviceName,
        },
        locale: 'en-us',
        network: {
          bluetooth: false,
          carrier: carrierName,
          cellular: true,
          wifi: true,
        },
        os: {
          name: getSystemName(),
          version: getSystemVersion(),
        },
        apptilePlatform: {
          bundleVersion: '',
          appId: apptileStateSelector(ApptileAnalytics.store.getState())?.appId as string,
          appSaveId: apptileStateSelector(ApptileAnalytics.store.getState())?.appSaveId as string,
        },
        externalId: [
          {
            type: 'ga4AppInstanceId',
            id: firebaseAppInstanceID,
          },
        ],
        screen: {
          ...screenDimensions,
        },
      };
      logger.info('Analytics Context', ApptileAnalytics._context);
    } catch (e) {
      logger.error(e);
    }
  };

  static sendEvent = (eventType: ApptileAnalyticsEventType, name: string, data: any): void => {
    const userId: string = ApptileLocalSettings.userIdentifier;
    const event: IApptileAnalyticsEvent = {
      userId,
      context: ApptileAnalytics._context,
      event: name,
      properties: data,
      type: eventType,
      timestamp: new Date().toUTCString(),
    };

    // ! Todo: Event deduplication using UUID or Timestamp instead
    ApptileAnalytics._eventsBuffer.push(event);
    if (ApptileAnalytics._context) ApptileAnalytics.flushEvents();
  };

  static sendInternalEvent = (eventType: ApptileAnalyticsEventType, name: string, data: any): void => {
    Segment.sendEvent(eventType, name, data);
  };

  static flushEvents = async () => {
    if (ApptileAnalytics._context && ApptileAnalytics.store) {
      while (ApptileAnalytics._eventsBuffer.length) {
        let eventToDispatch = ApptileAnalytics._eventsBuffer.shift();
        if (eventToDispatch) {
          if (!apptileStateSelector(ApptileAnalytics.store.getState())?.appId) {
            await ApptileAnalytics.initialize();
          }
          eventToDispatch.context = ApptileAnalytics._context;
          await ApptileAnalytics.handleEvent(eventToDispatch);
        }
      }
    }
  };

  static handleEvent = async (apptileEvent: IApptileAnalyticsEvent) => {
    try {
      const {type, event, properties} = apptileEvent;
      // const appId = await RNGetValues('APPTILE_APP_ID');
      // Third party stream
      await InteractionManager.runAfterInteractions();
      Promise.resolve()
        .then(() => sendToMoEngage(event, properties))
        .then(() => sendToAppsflyer(event, properties))
        .then(() => sendToOneSignal(event, properties))
        .then(() => sendToCleverTap(event, properties))
        .then(() => sendToKlaviyo(event, properties))
        .then(() => Firebase.sendEvent(event, properties))
        .then(() => Facebook.sendEvent(event, properties))
        .then(() => Segment.sendEvent(type, event, properties));
    } catch (err) {
      logger.error(err);
    }
  };
}
