import _ from 'lodash';
import {OneSignal} from 'react-native-onesignal';

const prevEvent: [any, any] = [null, null];

export const sendToOneSignal = (eventName: string, eventData: Record<string, any>) => {
  if (!eventName) return;
  const _eventData = eventData?.toJS ? eventData.toJS() : eventData;
  if (_.isEqual(prevEvent, [eventName, _eventData])) return;
  prevEvent[0] = eventName;
  prevEvent[1] = _eventData;

  logger.info('sendToOneSignal', eventName, _eventData);

  // auth
  if (
    (eventName === 'customerLogIn' ||
      eventName === 'customerRegistered' ||
      eventName === 'login' ||
      eventName === 'signup') &&
    !_.isEmpty(_eventData?.emailId)
  ) {
    OneSignal.login(_eventData?.emailId);
    OneSignal.User.addEmail(_eventData?.emailId);
    // Parameter specified as non-null is null: method com.onesignal.user.internal.UserManager.addSms, parameter sms 1.6k Users 1.4k
    if (_eventData?.contactNumber) OneSignal.User.addSms(_eventData?.contactNumber);
    OneSignal.User.addTags({first_name: _eventData?.firstName, last_name: _eventData?.lastName});
  }
  if (eventName === 'customerLogout' || eventName === 'logout') {
    OneSignal.logout();
  }

  if (eventName === 'updateCartQuantity') {
    if (_eventData?.totalQuantity > 0) {
      let timestamp = Math.floor(Date.now() / 1000).toString();
      OneSignal.User.addTag('cart_update', timestamp);
    } else {
      OneSignal.User.removeTag('cart_update');
    }
  }

  if (eventName === 'purchase') {
    OneSignal.Session.addOutcomeWithValue('Purchase', _eventData?.totalValue);
    OneSignal.User.removeTag('cart_update');
  }
};

// Note: for more tags use this doc: https://documentation.onesignal.com/docs/add-user-data-tags
