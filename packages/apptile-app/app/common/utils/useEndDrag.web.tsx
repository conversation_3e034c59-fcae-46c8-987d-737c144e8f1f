import {useMemo} from 'react';
import {DragSourceMonitor} from 'react-dnd';
import {useDispatch, useSelector} from 'react-redux';
import {DispatchActions} from 'apptile-core';
import {CreateWidget, isCreateWidget, isMoveWidget, MoveWidget} from 'apptile-core';
import {sendTileAnalytics} from '@/root/web/actions/editorActions';
import {TILE_DROPPED} from '@/root/web/common/onboardingConstants';
import {changeOnboardingMetadata} from '@/root/web/actions/onboardingActions';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {getOnboardingMetadataWithKey} from '@/root/web/selectors/OnboardingSelector';

export default function useEndDrag(): (item: unknown, monitor: DragSourceMonitor) => void {
  const dispatch = useDispatch();
  const isTileDropped = useSelector((state: EditorRootState) => getOnboardingMetadataWithKey(state, TILE_DROPPED));
  return useMemo(
    () =>
      (item: unknown, monitor: DragSourceMonitor): void => {
        if (!monitor.didDrop()) {
          return;
        }
        if (!item.payload) {
          return;
        }
        if (isCreateWidget(item)) {
          const {payload} = item as CreateWidget;
          const {pluginType} = payload;
          if (pluginType == 'ModuleInstance') {
            const {moduleUUID} = payload;
            dispatch(sendTileAnalytics(moduleUUID ?? '', 'editor:design_tileDropped', {}));
            !isTileDropped && dispatch(changeOnboardingMetadata({[TILE_DROPPED]: true}));
          }
          dispatch({
            type: DispatchActions.ADD_PLUGIN,
            payload,
          });
          logger.info(`Create Widget ${pluginType} in ${payload.container} after ${payload.refWidget}`);
        } else if (isMoveWidget(item)) {
          const {payload} = item as MoveWidget;
          const {pluginId} = payload;
          dispatch({
            type: DispatchActions.PLUGIN_MOVE,
            payload,
          });
          logger.info(`Move Widget ${pluginId} in ${payload.container} after ${payload.refWidget}`);
        }
      },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isTileDropped],
  );
}
