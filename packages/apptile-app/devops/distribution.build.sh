#!/bin/bash
cd "$(dirname $0)/../"
project_path=$PWD
build_path="$PWD/build"

HOMEBREW_NO_AUTO_UPDATE=1 brew list jq &>/dev/null || brew install jq
HOMEBREW_NO_AUTO_UPDATE=1 brew list xmlstarlet &>/dev/null || brew install xmlstarlet

export env='prod'
build_android=$(jq -r '.build_android' "$project_path/devops/distribution.config.json")
build_ios=$(jq -r '.build_ios' "$project_path/devops/distribution.config.json")
build_core=$(jq -r '.build_core' "$project_path/devops/distribution.config.json")

# currentFrameworkVersion=$(jq -r '.version' "$project_path/package.json")

export currentFrameworkVersion="0.17.0" # hardcoding as of today. this should be taken from manifest call in future


export APPTILE_UPDATE_ENDPOINT=$(jq -r '.apptile_update_endpoint' "$project_path/devops/distribution.config.json")


config_path="$project_path/devops/distribution.config.json"
app_id=$(jq -r '.app_id' "$project_path/devops/distribution.config.json")

echo "Manifest Fetch Url: $APPTILE_UPDATE_ENDPOINT/app/$app_id/main/manifest?frameworkVersion=$currentFrameworkVersion"

echo -e "\n\nFetching App Config Meta Data...\n"

appconfig_manifest=$(curl --silent --location "$APPTILE_UPDATE_ENDPOINT/app/$app_id/main/manifest?frameworkVersion=$currentFrameworkVersion")

export published_commit_id=$(echo "$appconfig_manifest" | jq -r ".publishedCommitId")

export fork_name=$(echo "$appconfig_manifest" | jq -r ".forkName")

apptile_appconfig_endpoint=$(echo "$appconfig_manifest" | jq -r ".url")

echo "App Config Published Commit Id : $published_commit_id"

echo "App Config Fork Name : $fork_name"

echo "App Config Fetch Url: $apptile_appconfig_endpoint"
echo "Building with framework version: $currentFrameworkVersion."




echo -e "\n\n🧹 Cleaning build directory...\n"

rm -rf $build_path/


echo -e "\nFetching app info...\n"


curl -f -s --compressed --location "$apptile_appconfig_endpoint" -o $project_path/appConfig.json

# curl --location "https://api.apptile.io/api/app/$app_id/allPublished" -o $project_path/publishedVersions.json
# hasPublished=$(jq --arg version "$currentFrameworkVersion" '.activeAppSaves | has($version)' $project_path/publishedVersions.json)
# if [[ "$hasPublished" != "true" ]]; then
#     echo "There are no published versions available for $currentFrameworkVersion"
#     exit 1
# fi
# rm $project_path/publishedVersions.json

if [[ $? != 0 ]]
then
  echo "Error fetching app info. Exiting..."
echo -e "\033[0;31mCurrent Framework Version is a hardcoded value in the script. If your appconfig fetch fails, this can be the culprit or your app isn't published yet!"



  rm -f $project_path/appConfig.json
  exit 1
fi


echo -e "\n\nChecking if app uses camera...\n"

grep -q CameraWidget $project_path/appConfig.json
if [[ $? == 0 ]]
then
  usingCamera="true"
else
  usingCamera="false"
fi
export usingCamera

localCoreCmdArg=""
if [[ $build_core == "true" ]]
then
  localCoreCmdArg="use-local-core"
fi

if [[ $build_android == "true" ]]
then
    mv $project_path/android/app/src/main/assets/appConfig.json $project_path/appConfig.json.backup
    cp $project_path/appConfig.json $project_path/android/app/src/main/assets/appConfig.json
    $project_path/devops/scripts/android/distribution.build.sh $project_path $localCoreCmdArg
    mv $project_path/appConfig.json.backup $project_path/android/app/src/main/assets/appConfig.json
else
    echo "Skipping android build..."
fi


if [[ $build_ios == "true" ]]
then
    mv $project_path/ios/appConfig.json $project_path/appConfig.json.backup
    cp $project_path/appConfig.json $project_path/ios/appConfig.json
    $project_path/devops/scripts/ios/distribution.build.sh $project_path $localCoreCmdArg
    mv $project_path/appConfig.json.backup $project_path/ios/appConfig.json
else
    echo "Skipping ios build..."
fi


rm -f $project_path/appConfig.json
