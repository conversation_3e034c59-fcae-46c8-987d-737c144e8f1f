import {DispatchAction} from 'apptile-core';
import {
  FetchBuildsRequestPayload,
  FetchBuildsSuccessPayload,
  FetchBuildsFailurePayload,
} from '../store/BuildsReducer';

// Action Types
export const FETCH_BUILDS_REQUEST = 'FETCH_BUILDS_REQUEST';
export const FETCH_BUILDS_SUCCESS = 'FETCH_BUILDS_SUCCESS';
export const FETCH_BUILDS_FAILURE = 'FETCH_BUILDS_FAILURE';
export const CLEAR_BUILDS_STATE = 'CLEAR_BUILDS_STATE';

// Action Creators
export const fetchBuildsRequest = (appId: string): DispatchAction<FetchBuildsRequestPayload> => ({
  type: FETCH_BUILDS_REQUEST,
  payload: {appId},
});

export const fetchBuildsSuccess = (
  appId: string,
  buildsData: any[],
  buildsExist: boolean,
): DispatchAction<FetchBuildsSuccessPayload> => ({
  type: FETCH_BUILDS_SUCCESS,
  payload: {appId, buildsData, buildsExist},
});

export const fetchBuildsFailure = (appId: string, error: string): DispatchAction<FetchBuildsFailurePayload> => ({
  type: FETCH_BUILDS_FAILURE,
  payload: {appId, error},
});

export const clearBuildsState = () => ({
  type: CLEAR_BUILDS_STATE,
});
