import {Selector} from 'apptile-core';
import {ActionType, DispatchAction, DispatchActions, DispatchEmptyAction} from 'apptile-core';
import {
  BindingError,
  ModuleCreationParams,
  ModuleEditorLocationType,
  ModuleEditorRecord,
  ModuleRecord,
  PluginNamespace,
} from 'apptile-core';
import {ITileSaveInterface, ITileVariantSaveInterface, UpdateUserBody} from '../api/ApiTypes';
import {IDefaultFontContext} from '@/root/app/fonts/context';
import {IDefaultThemeContext} from '@/root/app/styles/theme/context';
import {BillingIntervalEnum} from '../views/subscription/PublishPricing';
import {EditorState} from '../common/webDatatypes';

export const TOGGLE_GENERALPURPOSE_MODAL = 'TOGGLE_GENERALPURPOSE_MODAL';

export const LOGIN_USER = 'LOGIN_USER';
export const LOGIN_USER_FINISHED = 'LOGIN_USER_FINISHED';
export const LOGIN_USER_ERROR = 'LOGIN_USER_ERROR';

export const REGISTER_USER = 'REGISTER_USER';
export const REGISTER_USER_FINISHED = 'REGISTER_USER_FINISHED';
export const REGISTER_USER_ERROR = 'REGISTER_USER_ERROR';

export const USER_INIT: ActionType = 'USER_INIT';
export const USER_INIT_ERROR: ActionType = 'USER_INIT_ERROR';
export const USER_INIT_UNAUTHORIZED: ActionType = 'USER_INIT_UNAUTHORIZED';

export const USER_INIT_FETCHED: ActionType = 'USER_INIT_FETCHED';

export const USER_EDIT: ActionType = 'USER_EDIT';
export const USER_EDIT_ERROR: ActionType = 'USER_EDIT_ERROR';
export const USER_EDIT_SUCCESS: ActionType = 'USER_EDIT_SUCCESS';

export const FETCH_MY_ADD_ONS_SUCCESS: ActionType = 'FETCH_MY_ADD_ONS_SUCCESS';
export const FETCH_MY_ADD_ONS_FAILED: ActionType = 'FETCH_MY_ADD_ONS_FAILED';
export const FETCH_MY_ADD_ONS: ActionType = 'FETCH_MY_ADD_ONS';

export const PLATFORM_INIT: ActionType = 'PLATFORM_INIT';
export const PLATFORM_INIT_SUCCESS: ActionType = 'PLATFORM_INIT_SUCCESS';
export const PLATFORM_INIT_FAILED: ActionType = 'PLATFORM_INIT_FAILED';

export const LOGOUT_USER: ActionType = 'LOGOUT_USER';

export const APP_SAVE: ActionType = 'APP_SAVE';
export const APP_SAVE_SUCCESS: ActionType = 'APP_SAVE_SUCCESS';
export const APP_SAVE_FAILED: ActionType = 'APP_SAVE_FAILED';

export const APP_UPDATE: ActionType = 'APP_UPDATE';
export const APP_UPDATE_SUCCESS: ActionType = 'APP_UPDATE_SUCCESS';
export const APP_UPDATE_FAILED: ActionType = 'APP_UPDATE_FAILED';

export const FETCH_ORGS: string = 'FETCH_ORGS';
export const FETCH_ORGS_SUCCESS: ActionType = 'FETCH_ORGS_SUCCESS';
export const FETCH_ORGS_FAILED: ActionType = 'FETCH_ORGS_FAILED';

export const FETCH_BRANCHES: string = 'FETCH_BRANCHES';
export const FETCH_BRANCHES_WITH_OTA: string = 'FETCH_BRANCHES_WITH_OTA';
export const FETCH_BRANCHES_WITH_OTA_SUCCESS: string = 'FETCH_BRANCHES_WITH_OTA_SUCCESS';
export const FETCH_BRANCHES_WITH_OTA_FAILED: string = 'FETCH_BRANCHES_WITH_OTA_FAILED';
export const FETCH_BRANCHES_SUCCESS: ActionType = 'FETCH_BRANCHES_SUCCESS';
export const FETCH_BRANCHES_FAILED: ActionType = 'FETCH_BRANCHES_FAILED';
export const DELETE_BRANCH: ActionType = 'DELETE_BRANCH';

export const FETCH_APP_FORKS: string = 'FETCH_APP_FORKS';
export const FETCH_APP_FORKS_SUCCESS: ActionType = 'FETCH_APP_FORKS_SUCCESS';
export const FETCH_APP_FORKS_FAILED: ActionType = 'FETCH_APP_FORKS_FAILED';

export const VERIFY_APP_FORKS: string = 'VERIFY_APP_FORKS';
export const VERIFY_APP_FORKS_SUCCESS: ActionType = 'VERIFY_APP_FORKS_SUCCESS';
export const VERIFY_APP_FORKS_FAILED: ActionType = 'VERIFY_APP_FORKS_FAILED';

export const FETCH_ORG_APPS: ActionType = 'FETCH_ORG_APPS';
export const FETCH_ORG_APPS_SUCCESS: ActionType = 'FETCH_ORG_APPS_SUCCESS';
export const FETCH_ORG_APPS_FAILED: ActionType = 'FETCH_ORG_APPS_FAILED';

export const CREATE_ORG_APP: ActionType = 'CREATE_ORG_APP';
export const CREATE_ORG_APP_SUCCESS: ActionType = 'CREATE_ORG_APP_SUCCESS';
export const CREATE_ORG_APP_FAILED: ActionType = 'CREATE_ORG_APP_FAILED';

export const DELETE_APP: ActionType = 'DELETE_APP';
export const DELETE_APP_SUCCESS: ActionType = 'DELETE_APP_SUCCESS';
export const DELETE_APP_FAILED: ActionType = 'DELETE_APP_FAILED';

export const REGISTER_PLUGINS: ActionType = 'REGISTER_PLUGINS';

export const PLUGIN_CONFIG_UPDATE: ActionType = 'PLUGIN_CONFIG_UPDATE';
export const PLUGIN_DELETE: ActionType = 'PLUGIN_DELETE';
export const PLUGIN_UPDATE_ID: ActionType = 'PLUGIN_UPDATE_ID';
export const MODULE_VARIANT_UPDATE: ActionType = 'MODULE_VARIANT_UPDATE';

export const EDITOR_OPEN_PROPERTY_INSPECTOR: ActionType = 'EDITOR_OPEN_PROPERTY_INSPECTOR';
export const EDITOR_OPEN_PLUGIN_LISTING: ActionType = 'EDITOR_OPEN_PLUGIN_LISTING';
export const EDITOR_OPEN_THEME_EDITOR: ActionType = 'EDITOR_OPEN_THEME_EDITOR';
export const EDITOR_OPEN_TILES_BROWSER: ActionType = 'EDITOR_OPEN_TILES_BROWSER';
export const EDITOR_SELECTED_PAGE_TYPE: ActionType = 'EDITOR_SELECTED_PAGE_TYPE';
export const EDITOR_SET_ACTIVE_ATTACHMENT_ID: ActionType = 'EDITOR_SET_ACTIVE_ATTACHMENT_ID';
export const EDITOR_SET_ACTIVE_ATTACHMENT_KEY: ActionType = 'EDITOR_SET_ACTIVE_ATTACHMENT_KEY';

export const EDITOR_SELECT_NAV_COMPONENT: ActionType = 'EDITOR_SELECT_NAV_COMPONENT';
export const EDITOR_NAVIGATION_REORDERING: ActionType = 'EDITOR_NAVIGATION_REORDERING';

export const EDITOR_EVENT_REORDER_UP: ActionType = 'EDITOR_EVENT_REORDER_UP';
export const EDITOR_EVENT_REORDER_DOWN: ActionType = 'EDITOR_EVENT_REORDER_DOWN';

export const EDITOR_SELECT_PAGE: ActionType = 'EDITOR_SELECT_PAGE';
export const EDITOR_GENERATE_PAGE_MODEL_CACHES: ActionType = 'EDITOR_GENERATE_PAGE_MODEL_CACHES';
export const EDITOR_CLEAR_PAGE_MODEL_CACHES: ActionType = 'EDITOR_CLEAR_PAGE_MODEL_CACHES';
export const EDITOR_RECORD_BINDING_ERROR: ActionType = 'EDITOR_RECORD_BINDING_ERROR';
export const EDITOR_RESOLVE_BINDING_ERROR: ActionType = 'EDITOR_RESOLVE_BINDING_ERROR';
export const EDITOR_TOGGLE_BINDING_ERRORS: ActionType = 'EDITOR_TOGGLE_BINDING_ERRORS';
export const EDITOR_TOGGLE_CHAT_VIEW: ActionType = 'EDITOR_TOGGLE_CHAT_VIEW';
export const EDITOR_OPEN_CHAT_VIEW: ActionType = 'EDITOR_OPEN_CHAT_VIEW';
export const EDITOR_SET_LANDING_PAGE_PROMPT: ActionType = 'EDITOR_SET_LANDING_PAGE_PROMPT';
export const EDITOR_CLOSE_CHAT_VIEW: ActionType = 'EDITOR_CLOSE_CHAT_VIEW';

export const EDITOR_COPY: ActionType = 'EDITOR_COPY';
export const EDITOR_PASTE: ActionType = 'EDITOR_PASTE';
export const CONFIG_INJECTION: ActionType = 'CONFIG_INJECTION';

export const EDITOR_OPEN_CODE_EDITOR: ActionType = 'EDITOR_OPEN_CODE_EDITOR';
export const EDITOR_CLOSE_CODE_EDITOR: ActionType = 'EDTOR_CLOSE_CODE_EDITOR';
export const EDITOR_OPEN_BINDING_EDITOR: ActionType = 'EDITOR_OPEN_BINDING_EDITOR';
export const EDITOR_CLOSE_BINDING_EDITOR: ActionType = 'EDITOR_CLOSE_BINDING_EDITOR';

export const SAVE_IMAGE_RECORD: ActionType = 'SAVE_IMAGE_RECORD';
export const IMPORT_IMAGE_RECORDS: ActionType = 'IMPORT_IMAGE_RECORDS';
export const SAVE_IMAGE_RECORD_FAILED: ActionType = 'SAVE_IMAGE_RECORD_FAILED';

export const CREATE_MODULE_FROM_WIDGET: ActionType = 'CREATE_MODULE_FROM_WIDGET';
export const SET_MODULE_CREATION_PARAMS: ActionType = 'SET_MODULE_CREATION_PARAMS';
export const EDITOR_OPEN_MODULE_DIALOG: ActionType = 'EDITOR_OPEN_MODULE_DIALOG';
export const EDITOR_CLOSE_MODULE_DIALOG: ActionType = 'EDITOR_CLOSE_MODULE_DIALOG';
export const SAVE_MODULE_RECORD: ActionType = 'SAVE_MODULE_RECORD';
export const DELETE_MODULE_RECORD: ActionType = 'DELETE_MODULE_RECORD';
export const DUPLICATE_MODULE_RECORD: ActionType = 'DUPLICATE_MODULE_RECORD';
export const UPDATE_MODULE_DEF_RECORD: ActionType = 'UPDATE_MODULE_DEF_RECORD';
export const SAVE_MODULE_VARIANT: ActionType = 'SAVE_MODULE_VARIANT';
export const ADD_MODULE_PLUGIN_PROPERTY_EDITOR: ActionType = 'ADD_MODULE_PLUGIN_PROPERTY_EDITOR';
export const DELETE_MODULE_PLUGIN_PROPERTY_EDITOR: ActionType = 'DELETE_MODULE_PLUGIN_PROPERTY_EDITOR';
export const FORWARD_MODULE_PLUGIN_PROPERTY: ActionType = 'FORWARD_MODULE_PLUGIN_PROPERTY';
export const UPDATE_MODULE_PLUGIN_PROPERTY: ActionType = 'UPDATE_MODULE_PLUGIN_PROPERTY';
export const UPDATE_MODULE_PROPERTY: ActionType = 'UPDATE_MODULE_PROPERTY';

export const REORDER_MODULE_PLUGIN_PROPERTIES: ActionType = 'REORDER_MODULE_PLUGIN_PROPERTIES';
export const FILL_MODULE_MANDATORY_FIELDS: ActionType = 'FILL_MODULE_MANDATORY_FIELDS';
export const MODULE_MANDATORY_FIELDS_FINISHED: ActionType = 'MODULE_MANDATORY_FIELDS_FINISHED';
export const MODULE_MANDATORY_FIELDS_FAILED: ActionType = 'MODULE_MANDATORY_FIELDS_FAILED';
export const CLEAN_MODULE_AUTO_FILL: ActionType = 'CLEAN_MODULE_AUTO_FILL';

export const CHANGE_APP_CONTEXT_DATA: ActionType = 'CHANGE_APP_CONTEXT_DATA';

export const EXPORT_TILE = 'EXPORT_TILE';
export const UPDATE_TILE_INFO = 'UPDATE_TILE_INFO';
export const UPDATE_TILE_DEFINITION = 'UPDATE_TILE_DEFINITION';
export const EXPORT_TILE_VARIANT = 'EXPORT_TILE_VARIANT';
export const UPDATE_TILE_VARIANT_INFO = 'UPDATE_TILE_VARIANT_INFO';
export const UPDATE_TILE_VARIANT_DEFINITION = 'UPDATE_TILE_VARIANT_DEFINITION';
export const FETCH_TILE = 'FETCH_TILE';
export const FETCHED_TILE_RECORD = 'FETCHED_TILE_RECORD';
export const EXPORT_PAGE = 'EXPORT_PAGE';
export const UPDATE_PAGE_INFO = 'UPDATE_PAGE_INFO';
export const UPDATE_PAGE_DEFINITION = 'UPDATE_PAGE_DEFINITION';
export const EXPORT_BLUEPRINT = 'EXPORT_BLUEPRINT';
export const REPLACE_APP_CONFIG = 'REPLACE_APP_CONFIG';
export const UPDATE_BLUEPRINT_INFO = 'UPDATE_BLUEPRINT_INFO';
export const UPDATE_BLUEPRINT_DEFINITION = 'UPDATE_BLUEPRINT_DEFINITION';
export const FETCH_PAGE = 'FETCH_PAGE';
export const SET_TEMP_PAGE_DATA = 'SET_TEMP_PAGE_DATA';
export const CLEAR_TEMP_PAGE_DATA = 'CLEAR_TEMP_PAGE_DATA';
export const HANDLE_PAGE_SELECTION = 'HANDLE_PAGE_SELECTION';
export const GET_INTEGRATION_PAGES = 'GET_INTEGRATION_PAGES';
export const GET_INTEGRATION_TILES = 'GET_INTEGRATION_TILES';
export const SET_TEMP_TILES_DATA = 'SET_TEMP_TILES_DATA';

export const GET_BLUEPRINTS = 'GET_BLUEPRINTS';
export const SET_BLUEPRINT_DATA = 'SET_BLUEPRINT_DATA';

export const FETCH_APP_ASSETS: ActionType = 'FETCH_APP_ASSETS';
export const FETCH_VIDEO_ASSETS: ActionType = 'FETCH_VIDEO_ASSETS';
export const FETCH_VIDEO_ASSETS_SUCCESS: ActionType = 'FETCH_VIDEO_ASSETS_SUCCESS';
export const FETCH_NEXT_VIDEO_ASSETS_SUCCESS: ActionType = 'FETCH_NEXT_VIDEO_ASSETS_SUCCESS';
export const FETCH_VIDEO_ASSETS_FAILED: ActionType = 'FETCH_VIDEO_ASSETS_FAILED';
export const FETCH_APP_ASSETS_LOAD_MORE: ActionType = 'FETCH_APP_ASSETS_LOAD_MORE';

export const FETCH_APP_ASSETS_SUCCESS: ActionType = 'FETCH_APP_ASSETS_SUCCESS';
export const FETCH_APP_ASSETS_FAILED: ActionType = 'FETCH_APP_ASSETS_FAILED';
export const DESTROY_APP_ASSETS: ActionType = 'DESTROY_APP_ASSETS';

export const UPLOAD_APP_ASSET: ActionType = 'UPLOAD_APP_ASSET';
export const UPLOAD_APP_ASSET_PROGRESS: ActionType = 'UPLOAD_APP_ASSET_PROGRESS';
export const UPLOAD_APP_ASSET_SUCCESS: ActionType = 'UPLOAD_APP_ASSET_SUCCESS';
export const UPLOAD_APP_ASSET_FAILED: ActionType = 'UPLOAD_APP_ASSET_FAILED';

export const PERSIST_APP_ASSET: ActionType = 'PERSIST_APP_ASSET';
export const PERSIST_APP_ASSET_SUCCESS: ActionType = 'PERSIST_APP_ASSET_SUCCESS';
export const PERSIST_APP_ASSET_FAILED: ActionType = 'PERSIST_APP_ASSET_FAILED';

export const DELETE_IMAGE_RECORD: ActionType = 'DELETE_IMAGE_RECORD';
export const DELETE_IMAGE_RECORD_SUCCESS: ActionType = 'DELETE_IMAGE_RECORD_SUCCESS';
export const DELETE_IMAGE_RECORD_FAILED: ActionType = 'DELETE_IMAGE_RECORD_FAILED';

export const FETCH_PAYMENT_URL: ActionType = 'FETCH_PAYMENT_URL';
export const FETCH_ADD_ON_PAYMENT_URL: ActionType = 'FETCH_ADD_ON_PAYMENT_URL';
export const FETCH_PAYMENT_URL_SUCCESS: ActionType = 'FETCH_PAYMENT_URL_SUCCESS';
export const FETCH_PAYMENT_URL_FAILED: ActionType = 'FETCH_PAYMENT_URL_FAILED';

export const FETCH_PLANS_LIST: ActionType = 'FETCH_PLANS_LIST';
export const FETCH_PLANS_LIST_SUCCESS: ActionType = 'FETCH_SUBSCRIPTION_PLAN_SUCCESS';
export const FETCH_PLANS_LIST_FAILED: ActionType = 'FETCH_SUBSCRIPTION_PLAN_FAILED';

export const FETCH_MY_SUBSCRIPTION: ActionType = 'FETCH_MY_SUBSCRIPTION';
export const FETCH_MY_SUBSCRIPTION_SUCCESS: ActionType = 'FETCH_MY_SUBSCRIPTION_SUCCESS';
export const FETCH_MY_SUBSCRIPTION_FAILED: ActionType = 'FETCH_MY_SUBSCRIPTION_FAILED';

export const FETCH_PLAN: ActionType = 'FETCH_PLAN';
export const FETCH_PLAN_SUCCESS: ActionType = 'FETCH_PLAN_SUCCESS';
export const FETCH_PLAN_FAILED: ActionType = 'FETCH_PLAN_FAILED';
export const SET_EDITOR_FEATURES: ActionType = 'SET_EDITOR_FEATURES';
export const SET_OPEN_PREMIUM_MODAL: ActionType = 'SET_OPEN_PREMIUM_MODAL';

export const SET_OPEN_DEVICE_PREVIEW_QR_MODAL: ActionType = 'SET_OPEN_DEVICE_PREVIEW_QR_MODAL';
export const SET_QR_MODAL_PLATFORM_TYPE: ActionType = 'SET_QR_MODAL_PLATFORM_TYPE';

export const FETCH_CURRENT_APP: ActionType = 'FETCH_CURRENT_APP';
export const FETCH_CURRENT_APP_SUCCESS: ActionType = 'FETCH_CURRENT_APP_SUCCESS';
export const FETCH_CURRENT_APP_FAILED: ActionType = 'FETCH_CURRENT_APP_FAILED';

export const CONFIRM_CHARGE: ActionType = 'CONFIRM_CHARGE';
export const CONFIRM_CHARGE_SUCCESS: ActionType = 'CONFIRM_CHARGE_SUCCESS';
export const CONFIRM_CHARGE_FAILED: ActionType = 'CONFIRM_CHARGE_FAILED';

// Paddle Actions
export const PADDLE_INIT: ActionType = 'PADDLE_INIT';
export const PADDLE_INIT_SUCCESS: ActionType = 'PADDLE_INIT_SUCCESS';
export const PADDLE_INIT_FAILED: ActionType = 'PADDLE_INIT_FAILED';

export const PADDLE_FETCH_PLANS: ActionType = 'PADDLE_FETCH_PLANS';
export const PADDLE_FETCH_PLANS_SUCCESS: ActionType = 'PADDLE_FETCH_PLANS_SUCCESS';
export const PADDLE_FETCH_PLANS_FAILED: ActionType = 'PADDLE_FETCH_PLANS_FAILED';

export const PADDLE_OPEN_CHECKOUT: ActionType = 'PADDLE_OPEN_CHECKOUT';
export const PADDLE_CHECKOUT_SUCCESS: ActionType = 'PADDLE_CHECKOUT_SUCCESS';
export const PADDLE_CHECKOUT_FAILED: ActionType = 'PADDLE_CHECKOUT_FAILED';

export const PADDLE_ADD_CREDITS: ActionType = 'PADDLE_ADD_CREDITS';
export const PADDLE_ADD_CREDITS_SUCCESS: ActionType = 'PADDLE_ADD_CREDITS_SUCCESS';
export const PADDLE_ADD_CREDITS_FAILED: ActionType = 'PADDLE_ADD_CREDITS_FAILED';

// Active Subscription Actions
export const FETCH_ACTIVE_SUBSCRIPTION: ActionType = 'FETCH_ACTIVE_SUBSCRIPTION';
export const FETCH_ACTIVE_SUBSCRIPTION_SUCCESS: ActionType = 'FETCH_ACTIVE_SUBSCRIPTION_SUCCESS';
export const FETCH_ACTIVE_SUBSCRIPTION_FAILED: ActionType = 'FETCH_ACTIVE_SUBSCRIPTION_FAILED';
export const SET_ACTIVE_SUBSCRIPTION: ActionType = 'SET_ACTIVE_SUBSCRIPTION';

export const FETCH_INTEGRATION_LIST: ActionType = 'FETCH_INTEGRATION_LIST';
export const FETCH_INTEGRATION_CATEGORIES: ActionType = 'FETCH_INTEGRATION_CATEGORIES';
export const FETCH_INTEGRATION_LIST_SUCCESS: ActionType = 'FETCH_INTEGRATION_LIST_SUCCESS';
export const FETCH_INTEGRATION_CATEGORIES_SUCCESS: ActionType = 'FETCH_INTEGRATION_CATEGORIES_SUCCESS';
export const FETCH_INTEGRATION_LIST_FAILED: ActionType = 'FETCH_INTEGRATION_LIST_FAILED';

export const FETCH_INTEGRATION: ActionType = 'FETCH_INTEGRATION';
export const FETCH_INTEGRATION_SUCCESS: ActionType = 'FETCH_INTEGRATION_SUCCESS';
export const FETCH_INTEGRATION_FAILED: ActionType = 'FETCH_INTEGRATION_FAILED';

export const FETCH_APP_INTEGRATION: ActionType = 'FETCH_APP_INTEGRATION';
export const FETCH_APP_INTEGRATION_SUCCESS: ActionType = 'FETCH_APP_INTEGRATION_SUCCESS';
export const FETCH_APP_INTEGRATION_FAILED: ActionType = 'FETCH_APP_INTEGRATION_FAILED';

export const REMOVE_APP_INTEGRATION: ActionType = 'REMOVE_APP_INTEGRATION';

export const FETCH_APP_INTEGRATIONS: ActionType = 'FETCH_APP_INTEGRATIONS';
export const FETCH_APP_INTEGRATIONS_SUCCESS: ActionType = 'FETCH_APP_INTEGRATIONS_SUCCESS';
export const FETCH_APP_INTEGRATIONS_FAILED: ActionType = 'FETCH_APP_INTEGRATIONS_FAILED';

export const APP_INTEGRATION_PAGE_SUCCESS: ActionType = 'APP_INTEGRATION_PAGE_SUCCESS';

export const SAVE_APP_INTEGRATION_CREDENTIAL: ActionType = 'SAVE_APP_INTEGRATION_CREDENTIAL';
export const SAVE_APP_INTEGRATION_CREDENTIAL_SUCCESS: ActionType = 'SAVE_APP_INTEGRATION_CREDENTIAL_SUCCESS';
export const SAVE_APP_INTEGRATION_CREDENTIAL_FAILED: ActionType = 'SAVE_APP_INTEGRATION_CREDENTIAL_FAILED';

export const POPULATE_ALL_INTEGRATIONS: ActionType = 'POPULATE_ALL_INTEGRATIONS';
export const POPULATE_ALL_INTEGRATIONS_FAILED: ActionType = 'POPULATE_ALL_INTEGRATIONS_FAILED';

export const CREATE_APP_INTEGRATION_CREDENTIAL: ActionType = 'CREATE_APP_INTEGRATION_CREDENTIAL';
export const CREATE_APP_INTEGRATION_CREDENTIAL_SUCCESS: ActionType = 'CREATE_APP_INTEGRATION_CREDENTIAL_SUCCESS';
export const CREATE_APP_INTEGRATION_CREDENTIAL_FAILED: ActionType = 'CREATE_APP_INTEGRATION_CREDENTIAL_FAILED';

export const OPEN_APP_INTEGRATION_EDITOR: ActionType = 'OPEN_APP_INTEGRATION_EDITOR';
export const CLOSE_APP_INTEGRATION_EDITOR: ActionType = 'CLOSE_APP_INTEGRATION_EDITOR';
export const TOGGLE_APP_INTEGRATION_INFO: ActionType = 'TOGGLE_APP_INTEGRATION_INFO';
export const SAVE_INTEGRATION_CREDENTIALS: ActionType = 'SAVE_INTEGRATION_CREDENTIALS';
export const TOGGLE_APP_INTEGRATION_EDITOR: ActionType = 'TOGGLE_APP_INTEGRATION_EDITOR';

export const OPEN_INTEGRATION_MODAL: ActionType = 'OPEN_INTEGRATION_MODAL';
export const CLOSE_INTEGRATION_MODAL: ActionType = 'CLOSE_INTEGRATION_MODAL';
export const TOGGLE_DELETE_INTEGRATION_MODAL: ActionType = 'TOGGLE_DELETE_INTEGRATION_MODAL';
export const CLOSE_DELETE_INTEGRATION_MODAL: ActionType = 'CLOSE_DELETE_INTEGRATION_MODAL';

export const CONFIGURE_DATASOURCES = 'CONFIGURE_DATASOURCES';
export const CONFIGURE_DATASOURCES_FINISHED = 'CONFIGURE_DATASOURCES_FINISHED';

export const CONFIGURE_DATASOURCES_WILE_ONBOARDING = 'CONFIGURE_DATASOURCES_WILE_ONBOARDING';

export const CLEAR_DATASOURCES_CREDENTIALS = 'CLEAR_DATASOURCES_CREDENTIALS';
export const CLEAR_DATASOURCES_CREDENTIALS_FINISHED = 'CLEAR_DATASOURCES_CREDENTIALS_FINISHED';

export const SOFT_RESTART_EDITOR = 'SOFT_RESTART_EDITOR';
export const ADD_NEW_PAGE_IN_NAVIGATION = 'ADD_NEW_PAGE_IN_NAVIGATION';

export const SET_EDITOR_PAGE_PARAMS = 'SET_EDITOR_PAGE_PARAMS';

export const SEND_TILE_ANALYTICS = 'SEND_TILE_ANALYTICS';
export const SET_ORG_ID = 'SET_ORG_ID';

export const FETCH_APPCONFIG_AND_INIT = 'FETCH_APPCONFIG_AND_INIT';

export const SET_HAS_ONE_SIGNAL = 'SET_HAS_ONE_SIGNAL';
export const SET_ACTIVE_NOTIFICATION_PROVIDER = 'SET_ACTIVE_NOTIFICATION_PROVIDER';
export const CHECK_HAS_ONE_SIGNAL = 'CHECK_HAS_ONE_SIGNAL';
export const FETCH_ANALYTICS_URL = 'FETCH_ANALYTICS_URL';
export const SET_ANALYTICS_URL = 'SET_ANALYTICS_URL';

export const GENERATE_WEB_PREVIEW_START = 'GENERATE_WEB_PREVIEW_START';
export const GENERATE_WEB_PREVIEW_SUCCESS = 'GENERATE_WEB_PREVIEW_SUCCESS';
export const GENERATE_WEB_PREVIEW_FAILED = 'GENERATE_WEB_PREVIEW_FAILED';

export const GENERATE_MOBILE_PREVIEW_START = 'GENERATE_MOBILE_PREVIEW_START';
export const GENERATE_MOBILE_PREVIEW_SUCCESS = 'GENERATE_MOBILE_PREVIEW_SUCCESS';
export const GENERATE_MOBILE_PREVIEW_FAILED = 'GENERATE_MOBILE_PREVIEW_FAILED';
export const RESET_MOBILE_PREVIEW_STATUS = 'RESET_MOBILE_PREVIEW_STATUS';

export const CREATE_APP_BRANCH = 'CREATE_APP_BRANCH';

export const SCHEDULE_OTA = 'SCHEDULE_OTA';
export const SCHEDULE_OTA_SUCCESS = 'SCHEDULE_OTA_SUCCESS';
export const SCHEDULE_OTA_FAILED = 'SCHEDULE_OTA_FAILED';
export const CHECK_OVERLAPPING_OTAS = 'CHECK_OVERLAPPING_OTAS';
export const CHECK_OVERLAPPING_OTAS_SUCCESS = 'CHECK_OVERLAPPING_OTAS_SUCCESS';
export const CLEAR_SNAPSHOT_STATE = 'CLEAR_SNAPSHOT_STATE';
export const CREATE_BRANCH_META = 'CREATE_BRANCH_META';
export const ADD_FORCE_UPDATE_BANNER = 'ADD_FORCE_UPDATE_BANNER';

export const ADD_NEW_CUSTOM_PAGE_IN_NAVIGATION = 'ADD_NEW_CUSTOM_PAGE_IN_NAVIGATION';
// export const DELETE_CUSTOM_PAGE = 'DELETE_CUSTOM_PAGE';
export const NAVIGATE_TO_PAGE = 'NAVIGATE_TO_PAGE';

export const LOGIN_WITH_GOOGLE = 'LOGIN_WITH_GOOGLE';
export const LOGIN_WITH_GOOGLE_SUCCESS = 'LOGIN_WITH_GOOGLE_SUCCESS';
export const LOGIN_WITH_GOOGLE_ERROR = 'LOGIN_WITH_GOOGLE_ERROR';

// Add these new action types
export const START_APP_CREATION = 'START_APP_CREATION';
export const START_APP_CREATION_SUCCESS = 'START_APP_CREATION_SUCCESS';
export const START_APP_CREATION_ERROR = 'START_APP_CREATION_ERROR';

// Web Preview Modal Actions
export const OPEN_WEB_PREVIEW_MODAL = 'OPEN_WEB_PREVIEW_MODAL';
export const CLOSE_WEB_PREVIEW_MODAL = 'CLOSE_WEB_PREVIEW_MODAL';
export const SET_WEB_PREVIEW_LINK = 'SET_WEB_PREVIEW_LINK';
export const SET_GENERATE_WEB_PREVIEW_ERROR = 'SET_GENERATE_WEB_PREVIEW_ERROR';

// Token Credits Actions
export const FETCH_TOKEN_CREDITS = 'FETCH_TOKEN_CREDITS';
export const FETCH_TOKEN_CREDITS_SUCCESS = 'FETCH_TOKEN_CREDITS_SUCCESS';
export const FETCH_TOKEN_CREDITS_FAILED = 'FETCH_TOKEN_CREDITS_FAILED';

//////////////////////////////////////
// Action Creators
//////////////////////////////////////

export interface ICreateAppBranchPayload {
  title: string;
}
export const createAppBranch = (title: string): DispatchAction<ICreateAppBranchPayload> => {
  return {
    type: CREATE_APP_BRANCH,
    payload: {title},
  };
};
export interface SaveAppPayload {
  newSave: boolean;
  showToast: boolean;
  remark?: string;
  backup?: boolean;
  force?: boolean;
  saveWithCacheWithoutPublishing?: boolean;
}

export const saveAppState = (
  newSave: boolean = false,
  showToast: boolean = false,
  remark?: string,
  backup: boolean = false,
  force: boolean = false,
  saveWithCacheWithoutPublishing: boolean = false,
): DispatchAction<SaveAppPayload> => {
  return {
    type: APP_SAVE,
    payload: {newSave, showToast, remark, backup, force, saveWithCacheWithoutPublishing},
  };
};

export interface AppSaveSuccessPayload {
  appSaveId: number;
}
export interface UpdateAppPayload {
  publishedAppSaveId: number;
}

export const updateApp = (publishedAppSaveId: number): DispatchAction<UpdateAppPayload> => {
  return {
    type: APP_UPDATE,
    payload: {publishedAppSaveId},
  };
};

export interface FetchBlueprintPayload {
  blueprintId: string;
  appSaveId: string;
  appId: string;
  orgId?: string;
  onboarding?: boolean;
  themeShift?: boolean;
}

export const replaceAppConfig = (payload: FetchBlueprintPayload): DispatchAction<FetchBlueprintPayload> => {
  return {
    type: REPLACE_APP_CONFIG,
    payload: payload,
  };
};

export interface PluginUpdateIdPayload {
  pluginId: string;
  pageId: string;
  newPluginId: string;
  namespace?: PluginNamespace;
}

export const pluginUpdateId = (
  pluginId: string,
  pageId: string,
  newPluginId: string,
  namespace: PluginNamespace | undefined = undefined,
): DispatchAction<PluginUpdateIdPayload> => {
  return {
    type: PLUGIN_UPDATE_ID,
    payload: {
      pluginId,
      pageId,
      newPluginId,
      namespace,
    },
  };
};

export interface NavigationReoderingPayload {
  operation: string;
  path: any;
}

export const navigationReodering = (operation: string, path: string[]): DispatchAction<NavigationReoderingPayload> => {
  return {
    type: EDITOR_NAVIGATION_REORDERING,
    payload: {
      operation,
      path,
    },
  };
};

export interface PluginDeletePayload {
  pluginId: string;
  pageId: string;
}

export const pluginDelete = (pluginId: string, pageId: string): DispatchAction<PluginDeletePayload> => {
  return {
    type: PLUGIN_DELETE,
    payload: {
      pluginId,
      pageId,
    },
  };
};

export interface ModuleVariantPayload {
  pluginId: string;
  pageId: string;
  variant: ITileVariantSaveInterface;
}

export const moduleVariantUpdate = (
  pluginId: string,
  pageId: string,
  variant: ITileVariantSaveInterface,
): DispatchAction<ModuleVariantPayload> => {
  return {
    type: MODULE_VARIANT_UPDATE,
    payload: {
      pluginId,
      pageId,
      variant,
    },
  };
};

export const openPluginListing = (): DispatchEmptyAction => {
  return {
    type: EDITOR_OPEN_PLUGIN_LISTING,
  };
};

export const openThemeEditor = (): DispatchEmptyAction => {
  return {
    type: EDITOR_OPEN_THEME_EDITOR,
  };
};

export const openPropertyInspector = (): DispatchEmptyAction => {
  return {
    type: EDITOR_OPEN_PROPERTY_INSPECTOR,
  };
};
export const openTilesBrowser = (): DispatchEmptyAction => {
  return {
    type: EDITOR_OPEN_TILES_BROWSER,
  };
};

export const toggleBindingErrors = (): DispatchEmptyAction => {
  return {
    type: EDITOR_TOGGLE_BINDING_ERRORS,
  };
};

export const toggleChatView = (): DispatchEmptyAction => {
  return {
    type: EDITOR_TOGGLE_CHAT_VIEW,
  };
};

export const openChatView = (): DispatchEmptyAction => {
  return {
    type: EDITOR_OPEN_CHAT_VIEW,
  };
};

export const setLandingPagePrompt = (prompt: string): DispatchAction<string> => {
  return {
    type: EDITOR_SET_LANDING_PAGE_PROMPT,
    payload: prompt,
  };
};

export const closeChatView = (): DispatchEmptyAction => {
  return {
    type: EDITOR_CLOSE_CHAT_VIEW,
  };
};

export const recordBindingError = (
  binding: string,
  error: BindingError,
): DispatchAction<{binding: string; error: BindingError}> => {
  return {
    type: EDITOR_RECORD_BINDING_ERROR,
    payload: {
      binding,
      error,
    },
  };
};

export const deleteBindingError = (binding: string): DispatchAction<string> => {
  return {
    type: EDITOR_RESOLVE_BINDING_ERROR,
    payload: binding,
  };
};

export const editorCopyAction = (): DispatchEmptyAction => {
  return {
    type: EDITOR_COPY,
  };
};

export const editorPasteAction = (pasteString: string): DispatchAction<string> => {
  return {
    type: EDITOR_PASTE,
    payload: pasteString,
  };
};

export const injectPageConfig = (configString: string): DispatchAction<string> => {
  return {
    type: CONFIG_INJECTION,
    payload: configString,
  };
};

export const selectPageConfig = (pageId: string): DispatchAction<string> => {
  return {
    type: EDITOR_SELECT_PAGE,
    payload: pageId,
  };
};

export interface CreateModuleFromWidget {
  widgetId: string;
  pageId: string;
}

export interface PluginPropsAndEditors {
  subtype: string;
  tileConfig: {
    name: string;
    defaultProps: Record<
      string,
      {
        type: 'codeInput' | 'colorInput';
        label: string;
        defaultValue: any;
      }
    >;
  };
}

export function createModuleFromWidget(widgetId: string, pageId: string): DispatchAction<CreateModuleFromWidget> {
  return {
    type: CREATE_MODULE_FROM_WIDGET,
    payload: {widgetId, pageId},
  };
}

export function setModuleCreationParams(
  params: Partial<ModuleCreationParams>,
): DispatchAction<Partial<ModuleCreationParams>> {
  return {
    type: SET_MODULE_CREATION_PARAMS,
    payload: {...params},
  };
}

export function openModuleCreationDialog(): DispatchEmptyAction {
  return {type: EDITOR_OPEN_MODULE_DIALOG};
}

export function closeModuleCreationDialog(): DispatchEmptyAction {
  return {type: EDITOR_CLOSE_MODULE_DIALOG};
}

export interface ForwardModulePluginPropertyPayload {
  pageId: string;
  pluginId: string;
  selector: Selector;
  isForwarded: boolean;
  name: string;
  location?: ModuleEditorLocationType;
}
export function forwardModulePluginProperty(
  pageId: string,
  pluginId: string,
  selector: Selector,
  isForwarded: boolean,
  name: string,
  location: ModuleEditorLocationType,
): DispatchAction<ForwardModulePluginPropertyPayload> {
  return {
    type: FORWARD_MODULE_PLUGIN_PROPERTY,
    payload: {
      pageId,
      pluginId,
      isForwarded,
      selector,
      name,
      location,
    },
  };
}

export interface AddModulePluginPropertyPayload {
  moduleUUID: string;
  editorRecord: ModuleEditorRecord;
  location?: ModuleEditorLocationType;
}
export function addModulePluginProperty(
  moduleUUID: string,
  editorRecord: ModuleEditorRecord,
  location: ModuleEditorLocationType,
): DispatchAction<AddModulePluginPropertyPayload> {
  return {
    type: ADD_MODULE_PLUGIN_PROPERTY_EDITOR,
    payload: {
      moduleUUID,
      editorRecord,
      location,
    },
  };
}

export interface DeleteModulePluginPropertyPayload {
  moduleUUID: string;
  editorSelector: Selector;
  location?: ModuleEditorLocationType;
}
export function deleteModulePluginProperty(
  moduleUUID: string,
  editorSelector: Selector,
  location: ModuleEditorLocationType,
): DispatchAction<DeleteModulePluginPropertyPayload> {
  return {
    type: DELETE_MODULE_PLUGIN_PROPERTY_EDITOR,
    payload: {
      moduleUUID,
      editorSelector,
      location,
    },
  };
}

export interface UpdateModuleDefinitionPayload {
  moduleInstanceId: string;
  pageId: string;
  moduleUUID: string;
}

export function updateModuleDefinition(
  moduleInstanceId: string,
  pageId: string,
  moduleUUID: string,
): DispatchAction<UpdateModuleDefinitionPayload> {
  return {
    type: UPDATE_MODULE_DEF_RECORD,
    payload: {moduleInstanceId, pageId, moduleUUID},
  };
}
export interface SaveModuleVariantPayload {
  moduleInstanceId: string;
  pageId: string;
  moduleUUID: string;
}

export function saveModuleVariant(
  moduleInstanceId: string,
  pageId: string,
  moduleUUID: string,
): DispatchAction<SaveModuleVariantPayload> {
  return {
    type: SAVE_MODULE_VARIANT,
    payload: {moduleInstanceId, pageId, moduleUUID},
  };
}
export interface UpdateModulePluginPropertyPayload {
  moduleUUID: string;
  editorSelKey: string;
  location: ModuleEditorLocationType;
  update: any;
}
export function updateModulePluginProperty(
  moduleUUID: string,
  editorSelKey: string,
  location: ModuleEditorLocationType,
  update: any,
): DispatchAction<UpdateModulePluginPropertyPayload> {
  return {
    type: UPDATE_MODULE_PLUGIN_PROPERTY,
    payload: {
      moduleUUID,
      editorSelKey,
      location,
      update,
    },
  };
}
export interface UpdateModulePropertyPayload {
  moduleUUID: string;
  property: string;
  value: any;
}
export function updateModuleProperty(
  moduleUUID: string,
  property: string,
  value: any,
): DispatchAction<UpdateModulePropertyPayload> {
  return {
    type: UPDATE_MODULE_PROPERTY,
    payload: {
      moduleUUID,
      property,
      value,
    },
  };
}
export interface ReorderModulePluginPropertiesPayload {
  moduleUUID: string;
  location: ModuleEditorLocationType;
  keys: string[];
}
export function reorderModulePluginProperties(
  moduleUUID: string,
  location: ModuleEditorLocationType,
  keys: string[],
): DispatchAction<ReorderModulePluginPropertiesPayload> {
  return {
    type: REORDER_MODULE_PLUGIN_PROPERTIES,
    payload: {
      moduleUUID,
      location,
      keys,
    },
  };
}

export interface RemapModuleInstance {
  moduleUUID: string;
  moduleInstanceId: string;
  pageId: string;
}

export function remapModuleInstance(
  moduleUUID: string,
  moduleInstanceId: string,
  pageId: string,
): DispatchAction<RemapModuleInstance> {
  return {
    type: DispatchActions.REMAP_MODULE_INSTANCE,
    payload: {moduleUUID, moduleInstanceId, pageId},
  };
}

export const fillModuleMandtoryFields = (pageId: string, pluginId: string, moduleUUID: string, appId: any) => {
  return {
    type: FILL_MODULE_MANDATORY_FIELDS,
    payload: {pageId, pluginId, moduleUUID, appId},
  };
};

export const moduleMandtoryFieldsFailed = (pageId: string, pluginId: string) => {
  return {
    type: MODULE_MANDATORY_FIELDS_FAILED,
    payload: {pageId, pluginId},
  };
};

export const moduleMandtoryFieldsFinished = (pageId: string, pluginId: string) => {
  return {
    type: MODULE_MANDATORY_FIELDS_FINISHED,
    payload: {pageId, pluginId},
  };
};

export const cleanModuleAutoFill = (pageId: string, pluginId: string) => {
  return {
    type: CLEAN_MODULE_AUTO_FILL,
    payload: {pageId, pluginId},
  };
};

export interface ExportTilePayload {
  moduleUUID: string;
  tileDetails: Omit<ITileSaveInterface, 'data'>;
}
export interface ExportTileVariantPayload {
  moduleUUID: string;
  variantDetails: Omit<ITileVariantSaveInterface, 'data'>;
}
export interface ExportPagePayload {
  pageId: string;
  pageDetails: Omit<ITileSaveInterface, 'data'>;
}

export interface FetchPagePayload {
  tags: string[];
  isPageImportAction: boolean;
  saveMetaData: boolean;
  isDeleteAction: boolean;
}
export interface ExportBlueprintPayload {
  blueprintDetails: Omit<ITileSaveInterface, 'data'>;
}
export function exportTile(moduleUUID: string, tileDetails: ExportTilePayload['tileDetails']) {
  return {
    type: EXPORT_TILE,
    payload: {moduleUUID, tileDetails},
  };
}
export function exportTileVariant(moduleUUID: string, variantDetails: ExportTileVariantPayload['variantDetails']) {
  return {
    type: EXPORT_TILE_VARIANT,
    payload: {moduleUUID, variantDetails},
  };
}
export function exportPage(pageId: string, pageDetails: ExportPagePayload['pageDetails']) {
  return {
    type: EXPORT_PAGE,
    payload: {pageId, pageDetails},
  };
}
export function exportBlueprint(blueprintDetails: ExportBlueprintPayload['blueprintDetails']) {
  return {
    type: EXPORT_BLUEPRINT,
    payload: {blueprintDetails},
  };
}
export function updateTileInfo(moduleUUID: string, tileDetails: ExportTilePayload['tileDetails']) {
  return {
    type: UPDATE_TILE_INFO,
    payload: {moduleUUID, tileDetails},
  };
}
export function updateTileVariantInfo(moduleUUID: string, variantDetails: ExportTileVariantPayload['variantDetails']) {
  return {
    type: UPDATE_TILE_VARIANT_INFO,
    payload: {moduleUUID, variantDetails},
  };
}
export function updatePageInfo(pageId: string, pageDetails: ExportPagePayload['pageDetails']) {
  return {
    type: UPDATE_PAGE_INFO,
    payload: {pageId, pageDetails},
  };
}
export function updateBlueprintInfo(blueprintDetails: ExportBlueprintPayload['blueprintDetails']) {
  return {
    type: UPDATE_BLUEPRINT_INFO,
    payload: {blueprintDetails},
  };
}
export function updateTile(moduleUUID: string, tileDetails: ExportTilePayload['tileDetails']) {
  return {
    type: UPDATE_TILE_DEFINITION,
    payload: {moduleUUID, tileDetails},
  };
}
export function updateTileVariant(moduleUUID: string, variantDetails: ExportTileVariantPayload['variantDetails']) {
  return {
    type: UPDATE_TILE_VARIANT_DEFINITION,
    payload: {moduleUUID, variantDetails},
  };
}
export function updatePage(pageId: string, pageDetails: ExportPagePayload['pageDetails']) {
  return {
    type: UPDATE_PAGE_DEFINITION,
    payload: {pageId, pageDetails},
  };
}
export function updateBlueprint(blueprintDetails: ExportBlueprintPayload['blueprintDetails']) {
  return {
    type: UPDATE_BLUEPRINT_DEFINITION,
    payload: {blueprintDetails},
  };
}

export function fetchPage(tags: string[], isPageImportAction: boolean, saveMetaData: boolean, isDeleteAction: boolean) {
  return {
    type: FETCH_PAGE,
    payload: {tags, isPageImportAction, saveMetaData, isDeleteAction},
  };
}

export function getIntegrationTiles(tags: string[]) {
  return {
    type: GET_INTEGRATION_TILES,
    payload: {tags},
  };
}

export function setTempPageData(data = []) {
  return {
    type: SET_TEMP_PAGE_DATA,
    payload: {data},
  };
}

export function clearTempPageData() {
  return {
    type: CLEAR_TEMP_PAGE_DATA,
  };
}

export function handlePageSelection(id: string) {
  return {
    type: HANDLE_PAGE_SELECTION,
    payload: {id},
  };
}

export function getBlueprints(platFormType: string) {
  return {
    type: GET_BLUEPRINTS,
    payload: {platFormType},
  };
}

export function setBlueprintData(blueprints: any[]) {
  return {
    type: SET_BLUEPRINT_DATA,
    payload: {blueprints},
  };
}
export interface FetchTilePayload {
  moduleUUID: string;
  localDefinition: boolean;
}
export function fetchTile(moduleUUID: string, localDefinition: boolean) {
  return {
    type: FETCH_TILE,
    payload: {moduleUUID, localDefinition},
  };
}

export interface FetchedTileRecordPayload {
  moduleUUID: string;
  moduleRecord: ModuleRecord;
}
export function fetchedTileRecord(moduleUUID: string, moduleRecord: ModuleRecord) {
  return {
    type: FETCHED_TILE_RECORD,
    payload: {moduleUUID, moduleRecord},
  };
}
export interface LoginUserPayload {
  email: string;
  password: string;
}

export function loginUser(email: string, password: string): DispatchAction<LoginUserPayload> {
  return {
    type: LOGIN_USER,
    payload: {email: email, password: password},
  };
}

export function fetchOrgs(): DispatchEmptyAction {
  return {
    type: FETCH_ORGS,
  };
}

export function handleGetBlueprints(platFormType: string) {
  return {
    type: GET_BLUEPRINTS,
    payload: {platFormType},
  };
}

export interface FetchOrgAppsPayload {
  orgId: string;
}
export function fetchOrgApps(orgId: string): DispatchAction<FetchOrgAppsPayload> {
  return {
    type: FETCH_ORG_APPS,
    payload: {orgId},
  };
}

export interface FetchAppForksPayload {
  appId: string;
}
export function fetchAppForks(appId: string): DispatchAction<FetchAppForksPayload> {
  return {
    type: FETCH_APP_FORKS,
    payload: {appId},
  };
}

export interface VerifyAppForksPayload {
  appId: string;
}
export function verifyAppForks(appId: string): DispatchAction<VerifyAppForksPayload> {
  return {
    type: VERIFY_APP_FORKS,
    payload: {appId},
  };
}

export interface FetchAppBranchesPayload {
  appId: string;
  forkId: number;
}
export function fetchAppBranches(appId: string, forkId: number): DispatchAction<FetchAppBranchesPayload> {
  return {
    type: FETCH_BRANCHES,
    payload: {appId, forkId},
  };
}
export interface FetchAppBranchesWithScheduledOtaPayload {
  appId: string;
  forkId: number;
}
export function fetchAppBranchesWithScheduledOta(
  appId: string,
  forkId: number,
): DispatchAction<FetchAppBranchesWithScheduledOtaPayload> {
  return {
    type: FETCH_BRANCHES_WITH_OTA,
    payload: {appId, forkId},
  };
}

export interface DeleteBranchPayload {
  appId: string;
  forkId: number;
  branchName: string;
}
export function deleteAppBranch(
  appId: string,
  forkId: number,
  branchName: string,
): DispatchAction<DeleteBranchPayload> {
  return {
    type: DELETE_BRANCH,
    payload: {appId, forkId, branchName},
  };
}

export interface CreateOrgAppPayload {
  orgId: string | undefined;
  name: string | undefined;
  baseBlueprintId: string;
}
export function createOrgApp(
  orgId: string | undefined,
  name: string | undefined,
  baseBlueprintId: string,
): DispatchAction<CreateOrgAppPayload> {
  return {
    type: CREATE_ORG_APP,
    payload: {orgId, name, baseBlueprintId},
  };
}

export interface RegisterUserPayload {
  firstname: string;
  lastname: string;
  email: string;
  password: string;
  mapToOrg?: boolean; // Add this optional flag
}

export function registerUser(registerUserPayload: RegisterUserPayload): DispatchAction<RegisterUserPayload> {
  return {
    type: REGISTER_USER,
    payload: registerUserPayload,
  };
}

export interface DeleteOrgAppPayload {
  orgId: string;
  appId: string;
}

export function deleteOrgApp(orgId: string, appId: string): DispatchAction<DeleteOrgAppPayload> {
  return {
    type: DELETE_APP,
    payload: {orgId, appId},
  };
}

export function saveModuleTemplate(moduleUUID: string): DispatchAction<string> {
  return {
    type: SAVE_MODULE_RECORD,
    payload: moduleUUID,
  };
}

export function deleteModuleTemplate(moduleUUID: string): DispatchAction<string> {
  return {
    type: DELETE_MODULE_RECORD,
    payload: moduleUUID,
  };
}

export function duplicateModuleTemplate(moduleUUID: string): DispatchAction<string> {
  return {
    type: DUPLICATE_MODULE_RECORD,
    payload: moduleUUID,
  };
}

export interface FetchAppAssetsPayload {
  appId: string;
  page: number;
  limit?: number;
}

export function fetchAppAssets(appId: string, page: number, limit?: number): DispatchAction<FetchAppAssetsPayload> {
  return {
    type: FETCH_APP_ASSETS,
    payload: {appId, page, limit},
  };
}

export interface IFetchVideoAssetsPayload {
  nextCursor?: string;
}

export function fetchVideoAssets(nextCursor?: string): DispatchAction<IFetchVideoAssetsPayload> {
  return {
    type: FETCH_VIDEO_ASSETS,
    payload: {
      nextCursor,
    },
  };
}

export function fetchAppAssetsLoadMore(
  appId: string,
  page: number,
  limit?: number,
): DispatchAction<FetchAppAssetsPayload> {
  return {
    type: FETCH_APP_ASSETS_LOAD_MORE,
    payload: {appId, page, limit},
  };
}

export type UploadAppAsset = {
  file: File;
  metaData?: object;
};

export function uploadAppAsset({file, metaData}: UploadAppAsset): DispatchAction<{file: File; metaData: object}> {
  return {
    type: UPLOAD_APP_ASSET,
    payload: {file, metaData},
  };
}

export interface UploadAppAssetProgressPayload {
  fileName: string;
  progress: number;
}

export function uploadAppAssetProgress(
  fileName: string,
  progress: number,
): DispatchAction<UploadAppAssetProgressPayload> {
  return {
    type: UPLOAD_APP_ASSET_PROGRESS,
    payload: {fileName, progress},
  };
}

export interface PersistAppAssetPayload {
  fileKey: string;
  fileName: string;
}

export function persistAppAsset(fileKey: string, fileName: string): DispatchAction<PersistAppAssetPayload> {
  return {
    type: PERSIST_APP_ASSET,
    payload: {fileKey, fileName},
  };
}

export interface SaveImageRecordPayload {
  assetId: string;
}

export function saveImageRecord(assetId: string): DispatchAction<SaveImageRecordPayload> {
  return {
    type: SAVE_IMAGE_RECORD,
    payload: {assetId},
  };
}

export function userInit(): DispatchEmptyAction {
  return {
    type: USER_INIT,
  };
}

export function userEdit(user: UpdateUserBody): DispatchAction<UpdateUserBody> {
  return {
    type: USER_EDIT,
    payload: user,
  };
}

export interface PlatformInitPayload {
  platformHost: string;
  platformType: string;
  isEmbeddedInShopify: boolean;
  orgId?: string | null;
}

export function platformInit(
  platformHost: string,
  platformType: string,
  isEmbeddedInShopify = false,
  orgId: string | null = null,
): DispatchAction<PlatformInitPayload> {
  return {
    type: PLATFORM_INIT,
    payload: {platformHost, platformType, isEmbeddedInShopify, orgId},
  };
}

export interface ChangeAppContextPayload {
  appId: string;
}

export function changeAppContextData(appId: string): DispatchAction<ChangeAppContextPayload> {
  return {
    type: CHANGE_APP_CONTEXT_DATA,
    payload: {appId},
  };
}

export interface FetchPaymentUrl {
  planId: string;
  appId: string;
  billingInterval?: BillingIntervalEnum;
}

export function fetchPaymentUrl(
  planId: string,
  appId: string,
  billingInterval?: BillingIntervalEnum,
): DispatchAction<FetchPaymentUrl> {
  return {
    type: FETCH_PAYMENT_URL,
    payload: {planId, appId, billingInterval},
  };
}

export interface FetchAddOnPaymentUrl {
  addOnType: string;
  addOnCode: string;
  appId: string;
}

export function fetchAddOnPaymentUrl(
  addOnType: string,
  addOnCode: string,
  appId: string,
): DispatchAction<FetchAddOnPaymentUrl> {
  return {
    type: FETCH_ADD_ON_PAYMENT_URL,
    payload: {addOnType, addOnCode, appId},
  };
}

export interface SetEditorFeatures {
  currentPlanFeatures: string[];
}

export function setEditorFeatures(currentPlanFeatures: string[]): DispatchAction<SetEditorFeatures> {
  return {
    type: SET_EDITOR_FEATURES,
    payload: {currentPlanFeatures},
  };
}

export interface SetOpenPremiumModal {
  openPremiumModal: boolean;
  premiumModalPlan: string;
  upsellingFeature?: string;
}

export function setOpenPremiumModal(
  openPremiumModal: boolean,
  premiumModalPlan: string,
  upsellingFeature?: string,
): DispatchAction<SetOpenPremiumModal> {
  return {
    type: SET_OPEN_PREMIUM_MODAL,
    payload: {openPremiumModal, premiumModalPlan, upsellingFeature},
  };
}

export interface SetOpenDevicePreviewQRModal {
  openDevicePreviewQRModal: boolean;
}

export function setOpenDevicePreviewQRModal(
  openDevicePreviewQRModal: boolean,
): DispatchAction<SetOpenDevicePreviewQRModal> {
  return {
    type: SET_OPEN_DEVICE_PREVIEW_QR_MODAL,
    payload: {openDevicePreviewQRModal},
  };
}

export type DevicePlatformType = 'ANDROID' | 'IOS';
export interface SetQRModalPlatformType {
  platformType: DevicePlatformType;
}

export function setQRModalPlatformType(platformType: DevicePlatformType): DispatchAction<SetQRModalPlatformType> {
  return {
    type: SET_QR_MODAL_PLATFORM_TYPE,
    payload: {platformType},
  };
}

export function fetchPlanList(): DispatchEmptyAction {
  return {
    type: FETCH_PLANS_LIST,
  };
}

export interface FetchMyScubscriptionPayload {
  appId: string;
  organizationId: string;
}

export function fetchMySubscription(
  appId: string,
  organizationId: string,
): DispatchAction<FetchMyScubscriptionPayload> {
  return {
    type: FETCH_MY_SUBSCRIPTION,
    payload: {appId, organizationId},
  };
}

export interface FetchMyAddOnsPayload {
  appId: string;
}

export function fetchMyAddOns(appId: string): DispatchAction<FetchMyAddOnsPayload> {
  return {
    type: FETCH_MY_ADD_ONS,
    payload: {appId},
  };
}

export interface FetchPlanPayload {
  planId: string;
}
export function fetchPlan(planId: string): DispatchAction<FetchPlanPayload> {
  return {
    type: FETCH_PLAN,
    payload: {planId},
  };
}

export function fetchCurrentApp(): DispatchEmptyAction {
  return {
    type: FETCH_CURRENT_APP,
  };
}

export interface ConfirmChargePayload {
  chargeId: string;
  orgAddOnId: string | null;
}

export function confirmCharge(chargeId: string, orgAddOnId: string | null): DispatchAction<ConfirmChargePayload> {
  return {
    type: CONFIRM_CHARGE,
    payload: {chargeId, orgAddOnId},
  };
}

export interface EventReorderPayload {
  pluginId: string;
  pageId: string;
  eventIndex: number;
}

export const reorderEventUpward = (
  pluginId: string,
  pageId: string,
  eventIndex: number,
): DispatchAction<EventReorderPayload> => {
  return {
    type: EDITOR_EVENT_REORDER_UP,
    payload: {pluginId, pageId, eventIndex},
  };
};

export const reorderEventDownward = (
  pluginId: string,
  pageId: string,
  eventIndex: number,
): DispatchAction<EventReorderPayload> => {
  return {
    type: EDITOR_EVENT_REORDER_DOWN,
    payload: {pluginId, pageId, eventIndex},
  };
};

export interface deleteAppImagePayload {
  imageSelector: string[];
}

export const deleteAppImage = (imageSelector: string[]): DispatchAction<deleteAppImagePayload> => {
  return {
    type: DELETE_IMAGE_RECORD,
    payload: {imageSelector},
  };
};

export interface EditorActiveAttachmenIdPayload {
  activeAttachmentId: string;
}

export function editorSetActiveAttachmentId(attachmentId: string): DispatchAction<EditorActiveAttachmenIdPayload> {
  return {
    type: EDITOR_SET_ACTIVE_ATTACHMENT_ID,
    payload: {
      activeAttachmentId: attachmentId,
    },
  };
}

export interface EditorActiveAttachmentKeyPayload {
  activeAttachmentKey: string;
}

export function editorSetActiveAttachmentKey(attachmentKey: string): DispatchAction<EditorActiveAttachmentKeyPayload> {
  return {
    type: EDITOR_SET_ACTIVE_ATTACHMENT_KEY,
    payload: {
      activeAttachmentKey: attachmentKey,
    },
  };
}

export interface GeneratePageModelCachePayload {
  themeContext: IDefaultThemeContext;
  fontContext: IDefaultFontContext;
}

export function editorGeneratePageModelCaches(): DispatchEmptyAction {
  return {
    type: EDITOR_GENERATE_PAGE_MODEL_CACHES,
  };
}

export function editorClearPageModelCaches(): DispatchEmptyAction {
  return {
    type: EDITOR_CLEAR_PAGE_MODEL_CACHES,
  };
}

export interface FetchIntegrationsPayload {
  category?: string;
}

export function fetchIntegrations(category?: string): DispatchAction<FetchIntegrationsPayload> {
  return {
    type: FETCH_INTEGRATION_LIST,
    payload: {category},
  };
}

export function fetchIntegrationCategories(): DispatchEmptyAction {
  return {
    type: FETCH_INTEGRATION_CATEGORIES,
  };
}

export interface IFetchAppIntegrationPayload {
  appIntegrationId: string;
  appId: string;
}

export function fetchAppIntegration(
  appId: string,
  appIntegrationId: string,
): DispatchAction<IFetchAppIntegrationPayload> {
  return {
    type: FETCH_APP_INTEGRATION,
    payload: {
      appId,
      appIntegrationId,
    },
  };
}

export function toggleAppIntegration(
  appId: string,
  appIntegrationId: string,
  isActive: boolean,
  intergrationType: string,
  tempPages?: any[],
): DispatchAction<any> {
  return {
    type: REMOVE_APP_INTEGRATION,
    payload: {
      appId,
      appIntegrationId,
      isActive,
      intergrationType,
      tempPages,
    },
  };
}

export interface ISaveAppIntegrationCredentials {
  id: string;
  appId: string;
  credentials: Record<string, any>;
  platformType: string;
}

export function saveAppIntegrationCredentials(
  appId: string,
  id: string,
  credentials: Record<string, any>,
  platformType: string,
): DispatchAction<ISaveAppIntegrationCredentials> {
  return {
    type: SAVE_APP_INTEGRATION_CREDENTIAL,
    payload: {
      appId,
      id,
      credentials,
      platformType,
    },
  };
}

export interface ICreateAppIntegrationCredentials {
  appId: string;
  credentials: Record<string, any>;
  platformType: string;
  tempPages: any[];
}

export function createAppIntegrationCredentials(
  appId: string,
  credentials: Record<string, any>,
  platformType: string,
  tempPages: any[],
): DispatchAction<ICreateAppIntegrationCredentials> {
  return {
    type: CREATE_APP_INTEGRATION_CREDENTIAL,
    payload: {
      appId,
      credentials,
      platformType,
      tempPages,
    },
  };
}

export interface IFetchAppIntegrations {
  appId: string;
}

export function fetchAppIntegrations(appId: string): DispatchAction<IFetchAppIntegrations> {
  return {
    type: FETCH_APP_INTEGRATIONS,
    payload: {
      appId,
    },
  };
}

export interface IFetchIntegration {
  integrationId: string;
  appId: string;
}

export function fetchIntegration(appId: string, integrationId: string): DispatchAction<IFetchIntegration> {
  return {
    type: FETCH_INTEGRATION,
    payload: {
      appId,
      integrationId,
    },
  };
}

export interface IConfigureDatasourcesPayload {
  appId: string;
  forceUpdateSecrets: boolean;
  needsAppSave?: boolean;
}

export function configureDatasources(
  appId: string,
  forceUpdateSecrets: boolean = false,
  needsAppSave = false,
): DispatchAction<IConfigureDatasourcesPayload> {
  return {
    type: CONFIGURE_DATASOURCES,
    payload: {appId, forceUpdateSecrets, needsAppSave},
  };
}

//It will be used for onboarding / theme shift / etc...
export interface IConfigureDataSourcesWhileOnboardingPayload {
  appId: string;
  onboarding?: boolean;
  themeShift?: boolean;
}

export function configureDataSourcesWhileOnboarding(
  appId: string,
  onboarding: boolean = false,
  themeShift: boolean = false,
): DispatchAction<IConfigureDataSourcesWhileOnboardingPayload> {
  return {
    type: CONFIGURE_DATASOURCES_WILE_ONBOARDING,
    payload: {appId, onboarding, themeShift},
  };
}

export function clearDatasourcesCredentials(): DispatchEmptyAction {
  return {
    type: CLEAR_DATASOURCES_CREDENTIALS,
  };
}

export function softRestartConfig() {
  return {
    type: SOFT_RESTART_EDITOR,
  };
}

export interface SetEditorPageParamsPayload {
  pageKey: string;
  paramName: string;
  value: any;
}

export function setEditorPageParams(
  pageKey: string,
  paramName: string,
  value: any,
): DispatchAction<SetEditorPageParamsPayload> {
  return {
    type: SET_EDITOR_PAGE_PARAMS,
    payload: {pageKey, paramName, value},
  };
}

export function addNewPageInNavigation(screenId: string) {
  return {
    type: ADD_NEW_PAGE_IN_NAVIGATION,
    payload: {screenId},
  };
}

export interface IShowIntegrationEditorModal {
  appId: string;
}

export function showIntegrationEditorModal(appId: string): DispatchAction<IShowIntegrationEditorModal> {
  return {
    type: OPEN_APP_INTEGRATION_EDITOR,
    payload: {appId},
  };
}

export function closeIntegrationEditorModal(): DispatchEmptyAction {
  return {
    type: CLOSE_APP_INTEGRATION_EDITOR,
  };
}

export function toogleAppIntegrationEditor(appIntegrationEditorModalVisiblity: boolean): DispatchAction<any> {
  return {
    type: TOGGLE_APP_INTEGRATION_EDITOR,
    payload: {appIntegrationEditorModalVisiblity},
  };
}

export function toggleIntegratioInfoModal(appIntegrationInfoModalVisiblity: boolean): DispatchAction<any> {
  return {
    type: TOGGLE_APP_INTEGRATION_INFO,
    payload: {appIntegrationInfoModalVisiblity},
  };
}

export function toggleGeneralPurposeModal(
  visibility: boolean,
  message?: string,
): DispatchAction<{message: string | undefined}> {
  return {
    type: TOGGLE_GENERALPURPOSE_MODAL,
    payload: {message, visibility},
  };
}

export function handleSaveIntegrationCredentials(credentials: any): DispatchAction<any> {
  return {
    type: SAVE_INTEGRATION_CREDENTIALS,
    payload: {credentials},
  };
}

export interface IOpenIntegrationModal {
  appId: string;
  integrationId: string;
  isNewIntegration: boolean;
}

export function openIntegrationModal(
  integrationId: string,
  appId: string,
  isNewIntegration: boolean,
): DispatchAction<IOpenIntegrationModal> {
  return {
    type: OPEN_INTEGRATION_MODAL,
    payload: {appId, integrationId, isNewIntegration},
  };
}

export function closeIntegrationModal(): DispatchEmptyAction {
  return {
    type: CLOSE_INTEGRATION_MODAL,
  };
}

export function toggleDeleteIntegrationModal(deleteIntegrationModalVisibility: boolean): DispatchAction<any> {
  return {
    type: TOGGLE_DELETE_INTEGRATION_MODAL,
    payload: {
      deleteIntegrationModalVisibility,
    },
  };
}

export function closeDeleteIntegrationModal(): DispatchEmptyAction {
  return {
    type: CLOSE_DELETE_INTEGRATION_MODAL,
  };
}

export interface ISendTileAnalytics {
  moduleUUID: string;
  eventName: string;
  eventData: any;
}

export function sendTileAnalytics(
  moduleUUID: string,
  eventName: string,
  eventData: any,
): DispatchAction<ISendTileAnalytics> {
  return {
    type: SEND_TILE_ANALYTICS,
    payload: {moduleUUID, eventName, eventData},
  };
}

export interface ISetOrgId {
  orgId: string;
}
export function setOrgId(orgId: string): DispatchAction<ISetOrgId> {
  return {
    type: SET_ORG_ID,
    payload: {orgId},
  };
}
export interface FetchAppAndInitConfigPayload {
  appId: string | number;
  pageId: string;
  pageKey: string;
  pageConfig: string;
}

export function fetchAppConfigAndInitPageModel(
  appId: string | number,
  pageId: string,
  pageKey: string,
  pageConfig: string,
): DispatchAction<FetchAppAndInitConfigPayload> {
  return {
    type: FETCH_APPCONFIG_AND_INIT,
    payload: {
      pageId,
      pageKey,
      appId,
      pageConfig,
    },
  };
}

export function setHasOneSignal(appId: string) {
  return {
    type: CHECK_HAS_ONE_SIGNAL,
    payload: {
      appId,
    },
  };
}

export function setActiveNotificationProvider(provider: string) {
  return {
    type: SET_ACTIVE_NOTIFICATION_PROVIDER,
    payload: {
      provider,
    },
  };
}

export function fetchAnalyticsUrl() {
  return {
    type: FETCH_ANALYTICS_URL,
  };
}

export function setAnalyticsUrl(analyticsUrl: string, error: boolean = false) {
  return {
    type: SET_ANALYTICS_URL,
    payload: {analyticsUrl, error},
  };
}

export interface IScheduleOtaPayload {
  startDate: Date;
  endDate?: Date;
}

export function scheduleOta({startDate, endDate}: IScheduleOtaPayload) {
  return {
    type: SCHEDULE_OTA,
    payload: {startDate, endDate},
  };
}

export interface ICheckOverlappingOtas {
  forkId: number;
  startDate: Date;
}

export function checkOverlappingOtas(startDate: Date) {
  return {
    type: CHECK_OVERLAPPING_OTAS,
    payload: {startDate},
  };
}

export function clearSnapshotState() {
  return {
    type: CLEAR_SNAPSHOT_STATE,
  };
}

export function importImageRecord(pasteString: string): DispatchAction<any> {
  return {
    type: IMPORT_IMAGE_RECORDS,
    payload: {pasteString},
  };
}

export function addForceUpdateBanner(meta: {
  androidVersion: string;
  androidUrl: string;
  iosVersion: string;
  iosUrl: string;
}) {
  return {
    type: ADD_FORCE_UPDATE_BANNER,
    payload: meta,
  };
}
export function openBindingEditor(
  payload: Omit<EditorState['bindingEditor'], 'open'>,
): DispatchAction<EditorState['bindingEditor']> {
  // we pass a copy of the data into the editor so that any mutation on that data has no effect on the
  // model. Later on when the expression is evaluated in JSBindingEvaluator on the save event, it is
  // evaluated on a proxied object that will throw an error if mutation is peformed
  const payloadCopy = {
    _: payload.evalContext._,
    moment: payload.evalContext.moment,
    $pageContext: null,
    $appJSModel: null,
    i: payload.evalContext.i,
    currentPage: null,
  };

  try {
    payloadCopy.$pageContext = JSON.parse(JSON.stringify(payload.evalContext.$pageContext));
    payloadCopy.$appJSModel = JSON.parse(JSON.stringify(payload.evalContext.$appJSModel));
    payloadCopy.currentPage = JSON.parse(JSON.stringify(payload.evalContext.currentPage));
  } catch (err) {
    console.error('Failed to put page context copy');
  }
  return {
    type: EDITOR_OPEN_BINDING_EDITOR,
    payload: {
      open: true,
      binding: payload.binding,
      callback: payload.callback,
      namespace: payload.namespace,
      selector: payload.selector,
      evalContext: payloadCopy,
    },
  };
}

export function addNewPage(newScreenTitle: string) {
  return {
    type: ADD_NEW_CUSTOM_PAGE_IN_NAVIGATION,
    payload: {newScreenTitle},
  };
}

// export function deletePage(screenName: string, selector: [string]) {
//   return {
//     type: DELETE_CUSTOM_PAGE,
//     payload: {screenName, selector},
//   };
// }

export function navigateToPage(screenName: string, screenType: string) {
  return {
    type: NAVIGATE_TO_PAGE,
    payload: {screenName, screenType},
  };
}

export function loginWithGoogle(googleToken: string): DispatchAction<string> {
  return {
    type: LOGIN_WITH_GOOGLE,
    payload: googleToken,
  };
}

// Add this new action creator
export function startAppCreation(): DispatchAction<void> {
  return {
    type: START_APP_CREATION,
    payload: undefined,
  };
}

// Web preview generation action creators
export function generateWebPreviewStart(): DispatchEmptyAction {
  return {
    type: GENERATE_WEB_PREVIEW_START,
  };
}

export function generateWebPreviewSuccess(): DispatchEmptyAction {
  return {
    type: GENERATE_WEB_PREVIEW_SUCCESS,
  };
}

export function generateWebPreviewFailed(): DispatchEmptyAction {
  return {
    type: GENERATE_WEB_PREVIEW_FAILED,
  };
}

export function generateMobilePreviewStart(): DispatchEmptyAction {
  return {
    type: GENERATE_MOBILE_PREVIEW_START,
  };
}

export function generateMobilePreviewSuccess(): DispatchEmptyAction {
  return {
    type: GENERATE_MOBILE_PREVIEW_SUCCESS,
  };
}

export function generateMobilePreviewFailed(): DispatchEmptyAction {
  return {
    type: GENERATE_MOBILE_PREVIEW_FAILED,
  };
}

export function resetMobilePreviewStatus(): DispatchEmptyAction {
  return {
    type: RESET_MOBILE_PREVIEW_STATUS,
  };
}

// Web Preview Modal Action Creators
export function openWebPreviewModal(): DispatchEmptyAction {
  return {
    type: OPEN_WEB_PREVIEW_MODAL,
  };
}

export function closeWebPreviewModal(): DispatchEmptyAction {
  return {
    type: CLOSE_WEB_PREVIEW_MODAL,
  };
}

export function setWebPreviewLink(link: string | null): DispatchAction<{link: string | null}> {
  return {
    type: SET_WEB_PREVIEW_LINK,
    payload: {link},
  };
}

export function setGenerateWebPreviewError(error: string | null): DispatchAction<{error: string | null}> {
  return {
    type: SET_GENERATE_WEB_PREVIEW_ERROR,
    payload: {error},
  };
}

// Paddle Action Creators
export interface PaddleInitPayload {
  clientToken: string;
}

export function paddleInit(clientToken: string): DispatchAction<PaddleInitPayload> {
  return {
    type: PADDLE_INIT,
    payload: {clientToken},
  };
}

export function paddleFetchPlans(): DispatchEmptyAction {
  return {
    type: PADDLE_FETCH_PLANS,
  };
}

export interface PaddleOpenCheckoutPayload {
  planId: string;
  organizationId: string;
  userId: string;
  userEmail: string;
  credits: string;
}

export function paddleOpenCheckout(payload: PaddleOpenCheckoutPayload): DispatchAction<PaddleOpenCheckoutPayload> {
  return {
    type: PADDLE_OPEN_CHECKOUT,
    payload,
  };
}

// Active Subscription Action Creators
export interface FetchActiveSubscriptionPayload {
  organizationId: string;
}

export function fetchActiveSubscription(organizationId: string): DispatchAction<FetchActiveSubscriptionPayload> {
  return {
    type: FETCH_ACTIVE_SUBSCRIPTION,
    payload: {organizationId},
  };
}

export interface SetActiveSubscriptionPayload {
  subscription: any; // SubscriptionWithPlan | null
}

export function setActiveSubscription(subscription: any): DispatchAction<SetActiveSubscriptionPayload> {
  return {
    type: SET_ACTIVE_SUBSCRIPTION,
    payload: {subscription},
  };
}

export interface FetchTokenCreditsPayload {
  organizationId: string;
}

export function fetchTokenCredits(organizationId: string): DispatchAction<FetchTokenCreditsPayload> {
  return {
    type: FETCH_TOKEN_CREDITS,
    payload: { organizationId },
  };
}
