import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {FetchUserResponse} from './ApiTypes';
import {BillingIntervalEnum} from '../views/subscription/PublishPricing';

export default class BillingApi {
  static baseURL = '/api/subscription';
  static getApiUrl() {
    return Api.API_SERVER + BillingApi.baseURL;
  }

  static fetchPaymentUrl(
    planId: string,
    appId: string,
    billingInterval?: BillingIntervalEnum,
  ): AxiosPromise<FetchUserResponse> {
    return Api.put(
      BillingApi.getApiUrl() + `/plans/${planId}/subscribe?generatePaymentLink=true`,
      {billingInterval: billingInterval ?? BillingIntervalEnum.MONTHLY},
      {headers: {'X-App-Id': appId}},
    );
  }

  static fetchAddOnPaymentUrl(addOnType: string, addOnCode: string, appId: string): AxiosPromise<FetchUserResponse> {
    return Api.put(
      BillingApi.getApiUrl() + `/addOn/subscribe?type=${addOnType}&code=${addOnCode}`,
      {},
      {headers: {'X-App-Id': appId}},
    );
  }

  static fetchPlanList(): AxiosPromise<FetchUserResponse> {
    const partner = localStorage.getItem('apptile-partner-name');
    return Api.get(BillingApi.getApiUrl() + `/base-plans?platformType=${partner}`);
  }

  static fetchPlan(planId: string): AxiosPromise<FetchUserResponse> {
    return Api.get(BillingApi.getApiUrl() + `/plans/${planId}`);
  }

  static fetchMyPlan(appId: string, organizationId: string): AxiosPromise<FetchUserResponse> {
    const partner = localStorage.getItem('apptile-partner-name');
    return Api.get(BillingApi.getApiUrl() + `/my-plan?platformType=${partner}&organizationId=${organizationId}`, {headers: {'X-App-Id': appId}});
  }

  static fetchMyAddOns(appId: string): AxiosPromise<FetchUserResponse> {
    return Api.get(BillingApi.getApiUrl() + `/my-add-ons`, {headers: {'X-App-Id': appId}});
  }

  static confirmCharge(chargeId: string, orgAddOnId: string | null): AxiosPromise<FetchUserResponse> {
    const orgAddOn = orgAddOnId ? `?org_addon_id=${orgAddOnId}` : '';
    return Api.put(BillingApi.getApiUrl() + `/${chargeId}/confirm${orgAddOn}`);
  }
}
