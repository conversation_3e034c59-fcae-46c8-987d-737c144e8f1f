import axios from 'axios';
import _ from 'lodash';
import {WEB_API_SERVER_ENDPOINT} from '../../.env.json';
import {Api} from './Api';
import {logoutFromLively} from '@/root/web/actions/liveSellingActions';
import {store} from 'apptile-core';
import {makeToast} from '@/root/web/actions/toastActions';

const axiosInstance = axios.create();

axiosInstance.interceptors.response.use(
  response => response,
  function (error) {
    const statusCode = error?.response?.status || 0;
    if (statusCode && statusCode === 401) {
      store.dispatch(logoutFromLively());
      store.dispatch(makeToast({content: `Session expired. Please login again.`, appearances: 'warning'}));
      return null;
    } else {
      return Promise.reject(error);
    }
  },
);
export class LivelyApi {
  static baseURL = 'https://api.lively.li';
  static apptileServerBaseUrl = '/api/lively';

  static commonConfig = {
    withCredentials: false,
    headers: {
      'X-Request-Source': 'APPTILE',
    },
  };

  static getCommunicationUrl() {
    return LivelyApi.baseURL;
  }
  static getApptileServerCommunicationUrl() {
    return WEB_API_SERVER_ENDPOINT + LivelyApi.apptileServerBaseUrl;
  }
  static Register<T>(email: string, password: string, firstName: string, companyName: string) {
    return axiosInstance.post<T>(
      LivelyApi.getCommunicationUrl() + '/user/signup',
      {
        email: email,
        password: password,
        company_name: companyName,
        confirm_password: password,
        currency: '648aa6e70c324d17050f179f',
        first_name: firstName,
        last_name: 'Apptile',
        mobile_no: '1234567890',
        website: '1',
      },
      LivelyApi.commonConfig,
    );
  }
  static LivelyLogin<T>(email: string, password: string) {
    return axiosInstance.post<T>(LivelyApi.getCommunicationUrl() + '/user/login', {email: email, password: password});
  }
  static Login<T>(email: string, password: string) {
    return Api.post<T>(LivelyApi.getApptileServerCommunicationUrl() + '/login', {email: email, password: password});
  }
  static getShopifyCreds<T>(authToken: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() + `/shopify/get-shopify-credentials`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static getWidgets<T>(authToken: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() + `/widget`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static saveShopifyCreds<T>(authToken: string, accessToken: string, storeName: string) {
    return axiosInstance.post<T>(
      LivelyApi.getCommunicationUrl() + `/feeds/save-shopify-oauth`,
      {
        access_token: accessToken,
        marketplace_type: 'shopify',
        store_name: storeName,
      },
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static addMetadataToCompany<T>(authToken: string, meta: any) {
    return axiosInstance.post<T>(
      LivelyApi.getCommunicationUrl() + `/cms/company/meta-info`,
      {
        meta,
      },
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static getCompanyInfo<T>(authToken: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() + `/company/info`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }

  static getUpcomingStreams<T>(authToken: string, startDate: string, endDate: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() +
        `/live-streaming/status/1?page=1&sort_field=start_time_unix&start_date=${startDate}`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static getInProgressStreams<T>(authToken: string, startDate: string, endDate: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() +
        `/live-streaming/status/2?page=1&sort_field=start_time_unix&start_date=${startDate}`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static getPastStreams<T>(authToken: string, startDate: string, endDate: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() +
        `/live-streaming/status/3?page=1&limit=12&sort_order=desc&sort_field=start_time_unix&start_date=${startDate}`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static getUploadUrl<T>(authToken: string, extension: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() +
        `/live-streaming/upload/thumbnail?file_name=${new Date().getTime()}&extension=${extension}`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static changeStreamStatus<T>(authToken: string, streamingId: string, streamingStatus: string) {
    return axiosInstance.put<T>(
      LivelyApi.getCommunicationUrl() + `/live-streaming`,
      {streaming_id: streamingId, streaming_status: streamingStatus},
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static stopStream<T>(authToken: string, streamingId: string) {
    return axiosInstance.put<T>(
      LivelyApi.getCommunicationUrl() + `/live-streaming/end`,
      {streaming_id: streamingId, streaming_status: 4},
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static getFeeds<T>(authToken: string, currentPage: number = 1) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() + `/feeds?page=${currentPage}`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static deleteFeed<T>(authToken: string, feedId: string) {
    return axiosInstance.delete<T>(
      LivelyApi.getCommunicationUrl() + `/cms/feeds/delete`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
        data: {feed_id: feedId},
      }),
    );
  }
  static apptileStopStream<T>(authToken: string, streamId: string) {
    return axiosInstance.delete<T>(LivelyApi.getApptileServerCommunicationUrl() + `/delete-stream/${streamId}`, {
      headers: {
        authorization: `Bearer ${authToken}`,
      },
    });
  }
  static apptileArchiveStream<T>(authToken: string, streamId: string) {
    return axiosInstance.delete<T>(LivelyApi.getApptileServerCommunicationUrl() + `/archive-stream/${streamId}`, {
      headers: {
        authorization: `Bearer ${authToken}`,
      },
    });
  }
  static apptileGetLiveMetadata<T>(authToken: string, streamId: string) {
    return axiosInstance.get<T>(LivelyApi.getApptileServerCommunicationUrl() + `/stream-metadata/${streamId}`, {
      headers: {
        authorization: `Bearer ${authToken}`,
      },
    });
  }
  static archiveStream<T>(authToken: string, streamingId: string) {
    return axiosInstance.post<T>(
      LivelyApi.getCommunicationUrl() + `/live-streaming/archive`,
      {streaming_id: streamingId},
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static createLiveStream<T>(
    authToken: string,
    topic: string,
    description: string,
    thumbnailUrl: string,
    fbAuthId: string | null,
    streamToPlatforms: string[],
    streamProducts: string,
    startDate: number,
    endDate: number,
    meta: any = {},
  ) {
    return axiosInstance.post<T>(
      LivelyApi.getCommunicationUrl() + `/live-streaming`,
      {
        fb_auth_id: fbAuthId,
        start_time_unix: startDate,
        scheduled_end_time_unix: endDate,
        streaming_name: topic,
        streaming_description: description,
        streaming_thumbnail: thumbnailUrl,
        product_info: streamProducts,
        stream_to: streamToPlatforms,
        meta: meta,
      },
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static apptileCreateLiveStream<T>(
    authToken: string,
    topic: string,
    description: string,
    thumbnailUrl: string,
    fbAuthId: string | null,
    streamToPlatforms: string[],
    streamProducts: string,
    startDate: number,
    endDate: number,
    meta: any = {},
    hasNotification: boolean,
    notificationInfo: any = {},
    hasStoreCredit: boolean = false,
    storeCreditInfo: any = {},
  ) {
    return axiosInstance.post<T>(
      LivelyApi.getApptileServerCommunicationUrl() + `/create-stream`,
      {
        fb_auth_id: fbAuthId,
        start_time_unix: startDate,
        scheduled_end_time_unix: endDate,
        streaming_name: topic,
        streaming_description: description,
        streaming_thumbnail: thumbnailUrl,
        product_info: streamProducts,
        stream_to: streamToPlatforms,
        meta: meta,
        hasNotification: hasNotification,
        notificationInfo: notificationInfo,
        hasStoreCredit: hasStoreCredit,
        storeCreditInfo: storeCreditInfo,
      },
      {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      },
    );
  }
  static changeCurrentProduct<T>(authToken: string, streamingId: string, productId: string) {
    return axiosInstance.post<T>(
      LivelyApi.getCommunicationUrl() + `/live-streaming/insert-time-stamp`,
      {
        product_id: productId,
        streaming_id: streamingId,
        time_unix: Math.floor(Date.now() / 1000),
      },
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static addNewProductToStream<T>(authToken: string, streamingId: string, productInfo: any[]) {
    return axiosInstance.post<T>(
      LivelyApi.getCommunicationUrl() + `/live-streaming/meta`,
      {
        action: 'add_products',
        streaming_id: streamingId,
        product_info: productInfo,
      },
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static removeProductFromStream<T>(authToken: string, streamingId: string, productInfo: any[]) {
    return axiosInstance.post<T>(
      LivelyApi.getCommunicationUrl() + `/live-streaming/meta`,
      {
        action: 'remove_products',
        streaming_id: streamingId,
        product_info: productInfo,
      },
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static getLiveStreamInfoByID<T>(authToken: string, streamingId: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() + `/live-streaming/${streamingId}`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static getLiveStreamProductTime<T>(streamingId: string) {
    return axiosInstance.get<T>(LivelyApi.getCommunicationUrl() + `/live-streaming/product/time/${streamingId}`);
  }
  static generateZeegoToken<T>(authToken: string, userId: string, streamingId: string) {
    return axiosInstance.post<T>(
      LivelyApi.getCommunicationUrl() + `/live-streaming/token`,
      {
        user_id: userId,
        streaming_id: streamingId,
      },
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static getHostPublishingRights<T>(authToken: string, streamingId: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() + `/live-streaming/verify-host/${streamingId}`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static postStreamComments<T>(
    authToken: string,
    streamingId: string,
    streamingName: string,
    comments: string,
    platform: string,
  ) {
    return axiosInstance.post<T>(
      LivelyApi.getCommunicationUrl() + `/live-streaming/comments`,
      {
        platform: platform,
        streaming_id: streamingId,
        stream_info: {
          streaming_name: streamingName,
        },
        comments: comments,
      },
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static getStreamComments<T>(authToken: string, streamingId: string, platform: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() + `/live-streaming/comments?streaming_id=${streamingId}&platform=${platform}`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static getFacebookPages<T>(authToken: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() + `/facebook/auth`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static updateFacebookStatus<T>(authToken: string, fbAuthToken: string, fbUserId: string) {
    return axiosInstance.post<T>(
      LivelyApi.getCommunicationUrl() + `/facebook/auth`,
      {
        token: fbAuthToken,
        is_page: true,
        fb_user_id: fbUserId,
      },
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static getFacebookToken<T>(authToken: string, streamingId: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() + `/facebook/token/${streamingId}`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
        },
      }),
    );
  }
  static getFacebookComments<T>(fbToken: string, streamingId: string, after: string | null) {
    return axiosInstance.get<T>(
      `https://graph.facebook.com/v19.0/${streamingId}/comments?access_token=${fbToken}&fields=from,parent,message${
        after ? `&after=${after}` : ''
      }`,
    );
  }
  static sendFacebookComment<T>(fbToken: string, streamingId: string, message: string) {
    return axiosInstance.post<T>(
      `https://graph.facebook.com/v19.0/${streamingId}/comments`,
      {message},
      {
        headers: {
          Authorization: `Bearer ${fbToken}`,
          'Content-Type': 'application/json',
        },
      },
    );
  }
  static getClipsFromStream<T>(authToken: string, streamingId: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() +
        `/live-streaming/shoppable-feeds/chunks?streaming_id=${streamingId}&page=1&limit=250`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      }),
    );
  }
  static getProductStreamClips<T>(authToken: string, productId: string, companyId: string, filterStreamClips: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() +
        `/shoppable-feeds/cms/product/v2?store_product_id=${productId}&company_id=${companyId}&sf_type=${filterStreamClips}&page=1&limit=250`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      }),
    );
  }

  static updateProductClipRank<T>(authToken: string, productId: string, shoppableFeeds: any[]) {
    return axiosInstance.put<T>(
      LivelyApi.getCommunicationUrl() + `/shoppable-feeds/pdp/rank`,
      {
        product_id: productId,
        shoppable_feeds: shoppableFeeds,
      },
      _.merge(LivelyApi.commonConfig, {
        headers: {
          authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      }),
    );
  }

  static getProductStreamClipsPublicApi<T>(productId: string, companyId: string) {
    return axiosInstance.get<T>(
      LivelyApi.getCommunicationUrl() +
        `/shoppable-feeds/product/v2?store_product_id=${productId}&company_id=${companyId}`,
      _.merge(LivelyApi.commonConfig, {
        headers: {
          'Content-Type': 'application/json',
        },
      }),
    );
  }
}
