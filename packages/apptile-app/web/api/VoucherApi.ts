import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {IFetchIntegrationResponse} from './ApiTypes';

export default class VoucherApi {
  static baseURL = '/api';
  static getApiUrl() {
    return Api.API_SERVER + VoucherApi.baseURL;
  }

  static redeemVoucher(voucherCode:string, userEmail:string, organizationId:string): AxiosPromise<any> {
    return Api.post(VoucherApi.getApiUrl() + `/vouchers/redeem`, {
      voucherCode,
      userEmail,
      organizationId
    });
  }
}
