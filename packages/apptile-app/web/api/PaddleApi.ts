import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {
  PaddlePlan,
  PaddleCheckoutData,
  OrganizationCredits,
  Subscription,
  SubscriptionWithPlan,
  CreateSubscriptionRequest,
  BillingInterval,
} from './ApiTypes';

export default class PaddleApi {
  static baseURL = '/api/paddle';

  static getApiUrl() {
    return Api.API_SERVER + PaddleApi.baseURL;
  }

  // Fetch plans from Paddle
  static fetchPlans(): AxiosPromise<PaddlePlan[]> {
    return Api.get(PaddleApi.getApiUrl() + '/plans');
  }

  // Fetch products with their associated plans (for new pricing modal structure)
  static fetchProductsWithPlans(): AxiosPromise<any[]> {
    return Api.get(PaddleApi.getApiUrl() + '/products-with-plans');
  }

  // Fetch prices for specific products
  static fetchPrices(productIds: string[]): AxiosPromise<any[]> {
    return Api.post(PaddleApi.getApiUrl() + '/prices', {productIds});
  }

  // Verify checkout completion and add credits
  static verifyCheckout(checkoutData: PaddleCheckoutData): AxiosPromise<any> {
    return Api.post(PaddleApi.getApiUrl() + '/verify-checkout', checkoutData);
  }

  // Get organization credits
  static getOrganizationCredits(organizationId: string): AxiosPromise<OrganizationCredits> {
    return Api.get(PaddleApi.getApiUrl() + `/credits/${organizationId}`);
  }

  // Get credit usage history
  static getCreditHistory(organizationId: string): AxiosPromise<any[]> {
    return Api.get(PaddleApi.getApiUrl() + `/credits/${organizationId}/history`);
  }

  // Complete purchase and create subscription
  static completePurchase(purchaseData: {
    transactionId: string;
    priceId: string;
    organizationId: string;
    billingInterval: BillingInterval;
  }): AxiosPromise<Subscription> {
    return Api.post(PaddleApi.getApiUrl() + '/complete-purchase', purchaseData);
  }

  // Subscription management endpoints
  static createSubscription(data: CreateSubscriptionRequest): AxiosPromise<Subscription> {
    return Api.post(PaddleApi.getApiUrl() + '/subscribe', data);
  }

  static getOrganizationSubscriptions(organizationId: string): AxiosPromise<Subscription[]> {
    return Api.get(PaddleApi.getApiUrl() + `/subscriptions/${organizationId}`);
  }

  static getOrganizationSubscriptionsWithPlans(organizationId: string): AxiosPromise<SubscriptionWithPlan[]> {
    return Api.get(PaddleApi.getApiUrl() + `/subscriptions/${organizationId}?includePlans=true`);
  }

  static cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): AxiosPromise<Subscription> {
    return Api.post(PaddleApi.getApiUrl() + `/subscriptions/${subscriptionId}/cancel`, {
      cancelAtPeriodEnd,
    });
  }

  // Create free plan subscription
  static createFreePlanSubscription(organizationId: string): AxiosPromise<Subscription> {
    return Api.post(PaddleApi.getApiUrl() + '/free-plan', {
      organizationId,
    });
  }

  // Sync organization subscriptions with Paddle
  static syncOrganizationSubscriptions(
    organizationId: string,
  ): AxiosPromise<{message: string; organizationId: string}> {
    return Api.post(PaddleApi.getApiUrl() + `/sync-subscriptions/${organizationId}`);
  }

  // Generate customer portal URL
  static generateCustomerPortalUrl(organizationId: string): AxiosPromise<{url: string}> {
    return Api.post(PaddleApi.getApiUrl() + '/customer-portal', {
      organizationId,
    });
  }
}
