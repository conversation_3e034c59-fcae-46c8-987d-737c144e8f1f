import {AxiosPromise} from 'axios';
import {RegisterUserPayload} from '../actions/editorActions';
import {Api} from './Api';
import {FetchUserResponse, LoginUserResponse, LogoutResponse, UpdateUserBody} from './ApiTypes';

export default class UserApi {
  static baseURL = '/api/users';
  static getApiUrl() {
    return Api.API_SERVER + UserApi.baseURL;
  }

  static fetchUserInit(): AxiosPromise<FetchUserResponse> {
    return Api.get(UserApi.getApiUrl() + '/me');
  }

  static updateUserDetails(user: UpdateUserBody): AxiosPromise<FetchUserResponse> {
    return Api.put(UserApi.getApiUrl(), user);
  }

  static loginUser(email: string, password: string): AxiosPromise<LoginUserResponse> {
    return Api.post(UserApi.getApiUrl() + '/login', {email, password});
  }

  static registerUser(registerUserPayload: RegisterUserPayload): AxiosPromise<LoginUserResponse> {
    return Api.post(UserApi.getApiUrl() + '/register', registerUserPayload);
  }

  static loginWithGoogle(googleToken: string): AxiosPromise<LoginUserResponse> {
    return Api.post(UserApi.getApiUrl() + '/google-auth', {token: googleToken});
  }

  static logout(): AxiosPromise<LogoutResponse> {
    return Api.post(UserApi.getApiUrl() + '/logout', {});
  }
}
