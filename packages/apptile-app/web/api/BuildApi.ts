import {WEB_API_SERVER_ENDPOINT} from '@/root/.env.json';
import {Api} from '@/root/web/api/Api';

type IAppSettings = {
  bundleUrlScheme: string;
  displayName: string;
  bundleIdentifier: string;
  appHost: string;
};

type IAppMetadata = {
  appName: string;
  appSubtitle: string;
  privacyPolicy: string;
  appDescription: string;
  hasDeveloperAccount: boolean;
};

type IAppSecret = {
  secret: string;
  secretClass: 'keyAlias' | 'storePassword' | 'keyPassword' | 'teamId';
};

type IAppAsset = {
  uploaderKey: string;
  assetClass:
    | 'icon'
    | 'splash'
    | 'iosFirebaseServiceFile'
    | 'androidFirebaseServiceFile'
    | 'androidStoreFile'
    | 'firebaseServiceAccountKeyFile';
};

type IBuildConfig = {
  version: string;
  semVersion: string;
  frameworkVersion: string;
  platforms: string[];
  releaseNote?: string;
  triggeredBy?: string;
  triggeredByEmail?: string;
  isSDKBuild?: boolean;
  enableIpadSupport?: boolean;
  enableAppTrackingTransparency?: boolean;
  isIndividualAccount?: boolean;
  enableApptileAnalytics?: boolean;
  enableLivelyPIP?: boolean;
};

export class BuildManagerApi {
  static baseURL = '/build-system/api';

  static getBaseUrl() {
    return WEB_API_SERVER_ENDPOINT + BuildManagerApi.baseURL;
  }

  // Settings
  static getAppSettings<T>(appId: string) {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/settings/${appId}`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static postAppSettings<T>(appId: string, appSettings: IAppSettings) {
    return Api.post<T>(BuildManagerApi.getBaseUrl() + `/settings/${appId}`, appSettings, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static patchAppSettings<T>(appId: string, appSettings: IAppSettings) {
    return Api.patch<T>(BuildManagerApi.getBaseUrl() + `/settings/${appId}`, appSettings, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static updateAppSettings<T>(appId: string, appSettings: Partial<IAppSettings>) {
    return Api.put<T>(BuildManagerApi.getBaseUrl() + `/settings/${appId}`, appSettings, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static deleteAppSettings<T>(appId: string) {
    return Api.delete<T>(BuildManagerApi.getBaseUrl() + `/settings/${appId}`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  // Secrets
  static getAppSecret<T>(appId: string, secretId: string) {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/secret/${secretId}`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static getActiveAppSecret<T>(appId: string) {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/secret`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static postAppSecret<T>(appId: string, appSecret: IAppSecret) {
    return Api.post<T>(BuildManagerApi.getBaseUrl() + `/secret`, appSecret, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static deleteAppSecret<T>(appId: string, secretId: string) {
    return Api.delete<T>(BuildManagerApi.getBaseUrl() + `/secret/${secretId}`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  // Assets
  static getAppAsset<T>(appId: string, assetId: string) {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/assets/${assetId}`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static getActiveAppAsset<T>(appId: string) {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/assets`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static postAppAsset<T>(appId: string, appAsset: IAppAsset) {
    return Api.post<T>(BuildManagerApi.getBaseUrl() + `/assets`, appAsset, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static deleteAppAsset<T>(appId: string, assetId: string) {
    return Api.delete<T>(BuildManagerApi.getBaseUrl() + `/assets/${assetId}`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  // Build
  static getBuilds<T>(appId: string) {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/build/appDetails?platforms=android,ios`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static createBuild<T>(appId: string, buildConfig: IBuildConfig) {
    return Api.post<T>(BuildManagerApi.getBaseUrl() + `/build`, buildConfig, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static createReBuild<T>(appId: string, buildConfig: IBuildConfig, platform: 'android' | 'ios') {
    return Api.post<T>(BuildManagerApi.getBaseUrl() + `/build/rebuild?platform=${platform}`, buildConfig, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static uploadBuild<T>(appId: string, buildId: string, platform: string, isSDKBuild?: boolean) {
    return Api.post<T>(
      BuildManagerApi.getBaseUrl() + `/build/${buildId}/upload?platform=${platform}`,
      {
        isSDKBuild,
      },
      {
        headers: {
          'X-App-Id': appId,
        },
      },
    );
  }

  static enableIOSNotification<T>(appId: string, migrateToOneSignal: boolean) {
    return Api.post<T>(
      BuildManagerApi.getBaseUrl() + `/build/enableIOSNotification`,
      {migrateToOneSignal},
      {
        headers: {
          'X-App-Id': appId,
        },
      },
    );
  }

  static listBuild<T>(appId: string) {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/build`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static getBuildInfo<T>(appId: string, platformType: 'ios' | 'android', status: 'pending' | 'building') {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/build/${appId}/config`, {
      params: {
        platformType: platformType,
        status: status,
      },
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  //Metadata
  static getAppMetadata<T>(appId: string) {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/metadata`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static postAppMetadata<T>(appId: string, appMetadata: IAppMetadata) {
    return Api.post<T>(BuildManagerApi.getBaseUrl() + `/metadata`, appMetadata, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static patchAppMetadata<T>(appId: string, appMetadata: IAppMetadata) {
    return Api.patch<T>(BuildManagerApi.getBaseUrl() + `/metadata`, appMetadata, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static getLatestBuildVersion<T>(appId: string, platform: 'android' | 'ios') {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/build/latestBuildVersion?platform=${platform}`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static getLatestBuildConfig<T>(appId: string, platform: 'android' | 'ios') {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/build/latestBuildConfig?platform=${platform}`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static getTags<T>(appId: string) {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/git`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static getGITHeads<T>(appId: string, headType: 'tags' | 'branches') {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/git/${headType}`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static getAppConfigPublishStatus<T>(appId: string) {
    return Api.get<T>(WEB_API_SERVER_ENDPOINT + `/api/app/${appId}/publishStatus`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static getActiveFeatureFlags<T>(appId: string) {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/flags/active`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static verifyCredentials<T>(appId: string) {
    return Api.post<T>(
      BuildManagerApi.getBaseUrl() + `/v2/verify/ios/credentials`,
      {},
      {
        headers: {
          'X-App-Id': appId,
        },
      },
    );
  }

  static getAppStoreBundleIds<T>(appId: string) {
    return Api.get<T>(
      BuildManagerApi.getBaseUrl() + `/v2/verify/ios/BundleIds`,

      {
        headers: {
          'X-App-Id': appId,
        },
      },
    );
  }

  static checkIfAppExists<T>(appId: string, bundleId: string) {
    return Api.get<T>(
      BuildManagerApi.getBaseUrl() + `/v2/verify/ios/checkWetherAppExists?bundleId=${bundleId}`,

      {
        headers: {
          'X-App-Id': appId,
        },
      },
    );
  }

  static sendBuildAlert<T>(appId: string, metadata: any) {
    return Api.post<T>(BuildManagerApi.getBaseUrl() + `/v2/alerts/requestBuild`, metadata, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static sendAssetChangeAlert<T>(appId: string, metadata: any) {
    return Api.post<T>(BuildManagerApi.getBaseUrl() + `/v2/alerts/requestAssetChange`, metadata, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }

  static getBuildsV2<T>(appId: string) {
    return Api.get<T>(BuildManagerApi.getBaseUrl() + `/v2/builds`, {
      headers: {
        'X-App-Id': appId,
      },
    });
  }
}
