import {AppConfigResponse} from 'apptile-server';
import {AxiosPromise} from 'axios';
import {Api, currentAppConfigVersion} from './Api';
import { IManifestResponse } from './ApiTypes';
export default class AppApiV2 {
  static baseURL = '/api/v2/app';
  static getApiUrl() {
    return Api.API_SERVER + AppApiV2.baseURL;
  }

  static fetchAppForks(appUUID: string): AxiosPromise<AppConfigResponse> {
    if (!appUUID) {
      logger.warn('Cannot Fetch app forks for appUUID: ' + appUUID);
    }
    return Api.get(AppApiV2.getApiUrl() + `/${appUUID}/forks`);
  }

  static migrateAppToV2(appUUID: string): AxiosPromise<AppConfigResponse> {
    if (!appUUID) {
      logger.warn('Cannot migrate app for appUUID: ' + appUUID);
    }
    return Api.post(AppApiV2.getApiUrl() + `/${appUUID}/migrate?currentFrameworkVersion=${currentAppConfigVersion}`);
  }

  static createAppFork(appUUID: string, forkDetails: any): AxiosPromise<AppConfigResponse> {
    if (!appUUID) {
      logger.warn('Cannot create fork for appUUID: ' + appUUID);
    }
    return Api.post(AppApiV2.getApiUrl() + `/${appUUID}/fork`, {forkDetails});
  }
  
  static getManifest(appUUID:string): AxiosPromise<IManifestResponse> {
    return Api.get(AppApiV2.getApiUrl() + `/${appUUID}/manifest`);
  }
}
