import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {IFetchIntegrationResponse} from './ApiTypes';

export default class IntegrationsApi {
  static baseURL = '/api';
  static getApiUrl() {
    return Api.API_SERVER + IntegrationsApi.baseURL;
  }

  static refreshSdkIntegrations(payload: {appId: string; integrations: any}): AxiosPromise<any> {
    // TODO(gaurav): this should only happen when a new integration is added. Not on every page load.
    return fetch(global.PLUGIN_SERVER_URL + `/home/<USER>/refreshIntegrations`, {
      method: 'POST',
      body: JSON.stringify({integrations: payload.integrations}),
      headers: {
        'Content-Type': 'application/json',
      },
    }).then(res => res.json());
  }

  static fetchIntegration(appId: string, integrationId: string): AxiosPromise<IFetchIntegrationResponse> {
    return Api.get(IntegrationsApi.getApiUrl() + `/apps/${appId}/integrations/${integrationId}`);
  }

  static fetchIntegrationList(category?: string): AxiosPromise<IFetchIntegrationResponse> {
    const categoryParam = category ? `?category=${encodeURIComponent(category)}` : '/';
    return Api.get(IntegrationsApi.getApiUrl() + `/integrations` + categoryParam);
  }

  static fetchIntegrationCategories(): AxiosPromise<IFetchIntegrationResponse> {
    return Api.get(IntegrationsApi.getApiUrl() + `/integrations/categories`);
  }

  static fetchAppIntegrations(appId: string): AxiosPromise<IFetchIntegrationResponse> {
    return Api.get(IntegrationsApi.getApiUrl() + `/apps/${appId}/appIntegrations`);
  }

  static fetchAppIntegration(
    appId: string,
    id: string,
    isPlatformType: boolean = false,
  ): AxiosPromise<IFetchIntegrationResponse> {
    return Api.get(
      IntegrationsApi.getApiUrl() + `/apps/${appId}/appIntegrations/${id}?isPlatformType=${isPlatformType}`,
    );
  }

  static saveAppIntegrationCredentials(
    appId: string,
    id: string,
    credentials: any,
    isPlatformType: boolean = false,
  ): AxiosPromise<IFetchIntegrationResponse> {
    return Api.post(
      IntegrationsApi.getApiUrl() + `/apps/${appId}/appIntegrations/${id}/credentials?isPlatformType=${isPlatformType}`,
      credentials,
    );
  }

  static createIntegration(appId: string, data: any): AxiosPromise<IFetchIntegrationResponse> {
    return Api.post(IntegrationsApi.getApiUrl() + `/apps/${appId}/appIntegrations`, data);
  }

  static fetchAppIntegrationSecrets(appId: string, platformType: string): AxiosPromise<IFetchIntegrationResponse> {
    return Api.get(IntegrationsApi.getApiUrl() + `/apps/${appId}/appIntegrations/${platformType}/credentials`);
  }

  static toggleAppIntegration(
    appId: string,
    id: string,
    isActive: boolean,
    isPlatformType?: boolean | null | undefined,
  ): AxiosPromise<IFetchIntegrationResponse> {
    return Api.post(
      IntegrationsApi.getApiUrl() +
        `/apps/${appId}/appIntegrations/${id}/active/${isActive}${isPlatformType ? '?isPlatformType=true' : ''}`,
    );
  }

  /**
   * Update the credentials of a Supabase integration
   * @param appId The Apptile app ID
   * @param credentials The updated credentials
   */
  static updateCredentials(
    platformType: string,
    appId: string,
    credentials: any,
    isPlatformType?: boolean | null | undefined,
  ): AxiosPromise<IFetchIntegrationResponse> {
    return Api.post(
      IntegrationsApi.getApiUrl() +
        `/apps/${appId}/appIntegrations/${platformType}/credentials${isPlatformType ? '?isPlatformType=true' : ''}`,
      credentials,
    );
  }
}
