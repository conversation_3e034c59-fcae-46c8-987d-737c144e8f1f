PODS:
  - AnyCodable-FlightSchool (0.6.7)
  - boost (1.83.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - DoubleConversion (1.1.6)
  - DynamicFonts (0.1.2):
    - React-Core
  - FBLazyVector (0.73.8)
  - FBReactNativeSpec (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - RCTRequired (= 0.73.8)
    - RCTTypeSafety (= 0.73.8)
    - React-Core (= 0.73.8)
    - React-jsi (= 0.73.8)
    - ReactCommon/turbomodule/core (= 0.73.8)
  - Firebase/Analytics (10.27.0):
    - Firebase/Core
  - Firebase/Core (10.27.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.27.0)
  - Firebase/CoreOnly (10.27.0):
    - FirebaseCore (= 10.27.0)
  - Firebase/Messaging (10.27.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.27.0)
  - FirebaseAnalytics (10.27.0):
    - FirebaseAnalytics/AdIdSupport (= 10.27.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.27.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.27.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.27.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.27.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleAppMeasurement (10.27.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.27.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.27.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.27.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.27.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - KlaviyoSwift (3.1.0):
    - AnyCodable-FlightSchool
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - LogRocket (1.33.7)
  - logrocket-react-native (1.33.7):
    - LogRocket (= 1.33.7)
    - React
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - PromisesObjC (2.4.0)
  - RCT-Folly (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2022.05.16.00)
  - RCT-Folly/Default (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Fabric (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.73.8)
  - RCTTypeSafety (0.73.8):
    - FBLazyVector (= 0.73.8)
    - RCTRequired (= 0.73.8)
    - React-Core (= 0.73.8)
  - React (0.73.8):
    - React-Core (= 0.73.8)
    - React-Core/DevSupport (= 0.73.8)
    - React-Core/RCTWebSocket (= 0.73.8)
    - React-RCTActionSheet (= 0.73.8)
    - React-RCTAnimation (= 0.73.8)
    - React-RCTBlob (= 0.73.8)
    - React-RCTImage (= 0.73.8)
    - React-RCTLinking (= 0.73.8)
    - React-RCTNetwork (= 0.73.8)
    - React-RCTSettings (= 0.73.8)
    - React-RCTText (= 0.73.8)
    - React-RCTVibration (= 0.73.8)
  - React-callinvoker (0.73.8)
  - React-Codegen (0.73.8):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.8)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.8)
    - React-Core/RCTWebSocket (= 0.73.8)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.73.8)
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.8)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety (= 0.73.8)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.73.8)
    - React-jsi (= 0.73.8)
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.73.8)
    - ReactCommon
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.73.8):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.8)
    - React-debug (= 0.73.8)
    - React-jsi (= 0.73.8)
    - React-jsinspector (= 0.73.8)
    - React-logger (= 0.73.8)
    - React-perflogger (= 0.73.8)
    - React-runtimeexecutor (= 0.73.8)
  - React-debug (0.73.8)
  - React-Fabric (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.73.8)
    - React-Fabric/attributedstring (= 0.73.8)
    - React-Fabric/componentregistry (= 0.73.8)
    - React-Fabric/componentregistrynative (= 0.73.8)
    - React-Fabric/components (= 0.73.8)
    - React-Fabric/core (= 0.73.8)
    - React-Fabric/imagemanager (= 0.73.8)
    - React-Fabric/leakchecker (= 0.73.8)
    - React-Fabric/mounting (= 0.73.8)
    - React-Fabric/scheduler (= 0.73.8)
    - React-Fabric/telemetry (= 0.73.8)
    - React-Fabric/templateprocessor (= 0.73.8)
    - React-Fabric/textlayoutmanager (= 0.73.8)
    - React-Fabric/uimanager (= 0.73.8)
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.73.8)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.73.8)
    - React-Fabric/components/modal (= 0.73.8)
    - React-Fabric/components/rncore (= 0.73.8)
    - React-Fabric/components/root (= 0.73.8)
    - React-Fabric/components/safeareaview (= 0.73.8)
    - React-Fabric/components/scrollview (= 0.73.8)
    - React-Fabric/components/text (= 0.73.8)
    - React-Fabric/components/textinput (= 0.73.8)
    - React-Fabric/components/unimplementedview (= 0.73.8)
    - React-Fabric/components/view (= 0.73.8)
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired (= 0.73.8)
    - RCTTypeSafety (= 0.73.8)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-jsiexecutor (= 0.73.8)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-graphics (0.73.8):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core/Default (= 0.73.8)
    - React-utils
  - React-ImageManager (0.73.8):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jsc (0.73.8):
    - React-jsc/Fabric (= 0.73.8)
    - React-jsi (= 0.73.8)
  - React-jsc/Fabric (0.73.8):
    - React-jsi (= 0.73.8)
  - React-jserrorhandler (0.73.8):
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.73.8):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly (= 2022.05.16.00)
  - React-jsiexecutor (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact (= 0.73.8)
    - React-jsi (= 0.73.8)
    - React-perflogger (= 0.73.8)
  - React-jsinspector (0.73.8)
  - React-logger (0.73.8):
    - glog
  - React-Mapbuffer (0.73.8):
    - glog
    - React-debug
  - react-native-blur (4.3.0):
    - React-Core
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-compressor (1.8.25):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-get-random-values (1.11.0):
    - React-Core
  - react-native-keep-awake (4.0.0):
    - React
  - react-native-klaviyo (1.5.1):
    - KlaviyoSwift (= 3.1.0)
    - React-Core
  - react-native-pager-view (6.4.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-safe-area-context (4.10.5):
    - React-Core
  - react-native-video (5.2.0):
    - React-Core
    - react-native-video/Video (= 5.2.0)
  - react-native-video/Video (5.2.0):
    - React-Core
  - react-native-webview (13.10.4):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-zego-express-engine (3.14.5):
    - React
    - ZegoExpressEngine (= 3.14.5)
  - React-nativeconfig (0.73.8)
  - React-NativeModulesApple (0.73.8):
    - glog
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.73.8)
  - React-RCTActionSheet (0.73.8):
    - React-Core/RCTActionSheetHeaders (= 0.73.8)
  - React-RCTAnimation (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.73.8):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-jsc
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon
  - React-RCTBlob (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.73.8):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.73.8):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.73.8)
    - React-jsi (= 0.73.8)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.73.8)
  - React-RCTNetwork (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.73.8):
    - React-Core/RCTTextHeaders (= 0.73.8)
    - Yoga
  - React-RCTVibration (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - React-rncore (0.73.8)
  - React-runtimeexecutor (0.73.8):
    - React-jsi (= 0.73.8)
  - React-runtimescheduler (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-jsc
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - ReactCommon (0.73.8):
    - React-logger (= 0.73.8)
    - ReactCommon/turbomodule (= 0.73.8)
  - ReactCommon/turbomodule (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.8)
    - React-cxxreact (= 0.73.8)
    - React-jsi (= 0.73.8)
    - React-logger (= 0.73.8)
    - React-perflogger (= 0.73.8)
    - ReactCommon/turbomodule/bridging (= 0.73.8)
    - ReactCommon/turbomodule/core (= 0.73.8)
  - ReactCommon/turbomodule/bridging (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.8)
    - React-cxxreact (= 0.73.8)
    - React-jsi (= 0.73.8)
    - React-logger (= 0.73.8)
    - React-perflogger (= 0.73.8)
  - ReactCommon/turbomodule/core (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.8)
    - React-cxxreact (= 0.73.8)
    - React-jsi (= 0.73.8)
    - React-logger (= 0.73.8)
    - React-perflogger (= 0.73.8)
  - rn-fetch-blob (0.13.0-beta.1):
    - React-Core
  - RNCAsyncStorage (1.23.1):
    - React-Core
  - RNCClipboard (1.14.1):
    - React-Core
  - RNCMaskedView (0.3.1):
    - React-Core
  - RNCPushNotificationIOS (1.10.1):
    - React-Core
  - RNDeviceInfo (9.0.2):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.18.7)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBAnalytics (20.1.0):
    - Firebase/Analytics (= 10.27.0)
    - React-Core
    - RNFBApp
  - RNFBApp (20.1.0):
    - Firebase/CoreOnly (= 10.27.0)
    - React-Core
  - RNFBMessaging (20.1.0):
    - Firebase/Messaging (= 10.27.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.17.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNPermissions (3.3.1):
    - React-Core
  - RNReactNativeHapticFeedback (1.14.0):
    - React-Core
  - RNScreens (3.32.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - React-RCTImage
  - RNSentry (5.33.2):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - Sentry/HybridSDK (= 8.36.0)
  - RNSVG (15.3.0):
    - React-Core
  - RNVectorIcons (10.1.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - SDWebImage (5.18.12):
    - SDWebImage/Core (= 5.18.12)
  - SDWebImage/Core (5.18.12)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - segment-analytics-react-native (2.19.2):
    - React-Core
    - sovran-react-native
  - Sentry/HybridSDK (8.36.0)
  - SocketRocket (0.6.1)
  - sovran-react-native (1.1.1):
    - React-Core
  - Yoga (1.14.0)
  - ZegoExpressEngine (3.14.5)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - DynamicFonts (from `../node_modules/rn-dynamic-fonts`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase/Analytics
  - Firebase/Messaging
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleUtilities
  - "logrocket-react-native (from `../node_modules/@logrocket/react-native`)"
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jsc (from `../node_modules/react-native/ReactCommon/jsc`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - "react-native-blur (from `../node_modules/@react-native-community/blur`)"
  - react-native-camera (from `../node_modules/react-native-camera`)
  - react-native-compressor (from `../node_modules/react-native-compressor`)
  - react-native-get-random-values (from `../node_modules/react-native-get-random-values`)
  - react-native-keep-awake (from `../node_modules/react-native-keep-awake`)
  - react-native-klaviyo (from `../node_modules/react-native-klaviyo`)
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-render-html (from `../node_modules/react-native-render-html`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-video (from `../node_modules/react-native-video`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - react-native-zego-express-engine (from `../node_modules/zego-express-engine-reactnative`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCMaskedView (from `../node_modules/@react-native-masked-view/masked-view`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReactNativeHapticFeedback (from `../node_modules/react-native-haptic-feedback`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - "RNSentry (from `../node_modules/@sentry/react-native`)"
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - "segment-analytics-react-native (from `../node_modules/@segment/analytics-react-native`)"
  - "sovran-react-native (from `../node_modules/@segment/sovran-react-native`)"
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AnyCodable-FlightSchool
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - fmt
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - KlaviyoSwift
    - libwebp
    - LogRocket
    - nanopb
    - PromisesObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - Sentry
    - SocketRocket
    - ZegoExpressEngine

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  DynamicFonts:
    :path: "../node_modules/rn-dynamic-fonts"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  logrocket-react-native:
    :path: "../node_modules/@logrocket/react-native"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jsc:
    :path: "../node_modules/react-native/ReactCommon/jsc"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  react-native-blur:
    :path: "../node_modules/@react-native-community/blur"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-compressor:
    :path: "../node_modules/react-native-compressor"
  react-native-get-random-values:
    :path: "../node_modules/react-native-get-random-values"
  react-native-keep-awake:
    :path: "../node_modules/react-native-keep-awake"
  react-native-klaviyo:
    :path: "../node_modules/react-native-klaviyo"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-render-html:
    :path: "../node_modules/react-native-render-html"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-video:
    :path: "../node_modules/react-native-video"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  react-native-zego-express-engine:
    :path: "../node_modules/zego-express-engine-reactnative"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCMaskedView:
    :path: "../node_modules/@react-native-masked-view/masked-view"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReactNativeHapticFeedback:
    :path: "../node_modules/react-native-haptic-feedback"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSentry:
    :path: "../node_modules/@sentry/react-native"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  segment-analytics-react-native:
    :path: "../node_modules/@segment/analytics-react-native"
  sovran-react-native:
    :path: "../node_modules/@segment/sovran-react-native"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AnyCodable-FlightSchool: 261cbe76757802b17d471b9059b21e6fa5edf57b
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  DoubleConversion: fea03f2699887d960129cc54bba7e52542b6f953
  DynamicFonts: 6016c2e1796a4352d3502acc93e30a0a1b1d2a1a
  FBLazyVector: df34a309e356a77581809834f6ec3fbe7153f620
  FBReactNativeSpec: bbe8b686178e5ce03d1d8a356789f211f91f31b8
  Firebase: 26b040b20866a55f55eb3611b9fcf3ae64816b86
  FirebaseAnalytics: f9211b719db260cc91aebee8bb539cb367d0dfd1
  FirebaseCore: a2b95ae4ce7c83ceecfbbbe3b6f1cddc7415a808
  FirebaseCoreExtension: 705ca5b14bf71d2564a0ddc677df1fc86ffa600f
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 585984d0a1df120617eb10b44cad8968b859815e
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: c5d68082e772fa1c511173d6b30a9de2c05a69a2
  GoogleAppMeasurement: f65fc137531af9ad647f1c0a42f3b6a4d3a98049
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  KlaviyoSwift: 4f7af44278d08ab42a85f951ef57a9373be9181d
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  LogRocket: 8266b5f27cb0fe361d636c118e2d36eb3ab61b18
  logrocket-react-native: 21efcab7a7546aa0ecac17c865fc829bd0c6eeeb
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 7169b2b1c44399c76a47b5deaaba715eeeb476c0
  RCTRequired: 0c7f03a41ee32dec802c74c341e317a4165973d5
  RCTTypeSafety: 57698bb7fcde424922e201dab377f496a08a63e3
  React: 64c0c83924460a3d34fa5857ca2fd3ed2a69b581
  React-callinvoker: c0129cd7b8babac64b3d3515e72a8b2e743d261e
  React-Codegen: 726c7d55448d56aaa7de41afc6cba33744f93089
  React-Core: 990242bca7af8c8d498abbd49794cffba7876c90
  React-CoreModules: af9d2eca18c26b319157df21d5ea9eef7d5d4ad2
  React-cxxreact: 919cfbb5154b1fdf81fc19fe6c276d7126125a18
  React-debug: d5fb540b62f21497e4540be4158a1b555d3f4505
  React-Fabric: 0c280656e1031f02bef40d73078e0dc909855211
  React-FabricImage: 1ac1cb14c57ab63f7a9087942c7480a3777c8316
  React-graphics: b35a661b4a1416ea76a18b769451925a5c79503b
  React-ImageManager: 69cbe2a69c3c3aa15080927362e5f707077ffb24
  React-jsc: 0caac77822ec2c77415ab8f17187b3c0d0a7bfe9
  React-jserrorhandler: 11ecd2d8b5d3adb1a5008ab9309e286b38995e6a
  React-jsi: 37de7c012ee96339293dab2f421619c836b63c12
  React-jsiexecutor: eda9540a854c96584b4313557275a22e56b262cf
  React-jsinspector: 1729acf5ffe2d4439d698da25fddf0c75d07d1a1
  React-logger: 60afd40b183e8e6642bfd0108f1a1ad360cc665e
  React-Mapbuffer: 780a8ecda636bf19e5e2bfdbbb937e898ff39d7f
  react-native-blur: 50c9feabacbc5f49b61337ebc32192c6be7ec3c3
  react-native-camera: 3eae183c1d111103963f3dd913b65d01aef8110f
  react-native-compressor: 58eeb2c4c9dc4c898c7085d7e72a02f7ea4cfb64
  react-native-get-random-values: 21325b2244dfa6b58878f51f9aa42821e7ba3d06
  react-native-keep-awake: afad8a51dfef9fe9655a6344771be32c8596d774
  react-native-klaviyo: 2e6370f59402bf35e19365e68bbebf2a919d1bfd
  react-native-pager-view: 208ae8f40c243f7dad4b3156206e3ed1d0c3c74b
  react-native-render-html: 984dfe2294163d04bf5fe25d7c9f122e60e05ebe
  react-native-safe-area-context: a240ad4b683349e48b1d51fed1611138d1bdad97
  react-native-video: a4c2635d0802f983594b7057e1bce8f442f0ad28
  react-native-webview: ae503c7787c3bd5bdbda622209c45b961241b2fd
  react-native-zego-express-engine: 0dea531a42c910c9ccbc8647a54c4f68b27192fd
  React-nativeconfig: b42fe8e97a8d164aebeb6821a27076406b64fbb9
  React-NativeModulesApple: 9424cb02357f08c203115aa8cf6afd55cf81e75f
  React-perflogger: f9367428cf475f4606b5965c1d5a71781bb95299
  React-RCTActionSheet: 39b3248276c7f98e455aebb0cdd473a35c6d5082
  React-RCTAnimation: 10eee15d10e420f56b4014efd4d7fb7f064105fc
  React-RCTAppDelegate: 4ec79cabb94ca17bfcf583c56dd048902c69cf69
  React-RCTBlob: 6847593a75d314decdeea048c9d1c25f5bddcaba
  React-RCTFabric: bf24246a5bd06b802d2acb34237edffe66f8a09c
  React-RCTImage: e9c7790c25684ec5b64b4c92def4d6b95b225826
  React-RCTLinking: d3d3ea5596c9f30fa0e8138e356258fca1f2ccaf
  React-RCTNetwork: b52bcb51f559535612957f20b4ca28ff1438182f
  React-RCTSettings: 6763f9d5210ce3dae6f892a0546c06738014025b
  React-RCTText: 6b8365ef043d3fc01848bb8de48ee9e67ba3bc47
  React-RCTVibration: aa85a228a382b66e8844c2f94cd3e734fa96d07a
  React-rendererdebug: bd9e657da8ad510e0826fc1b8d8137aa3def7c37
  React-rncore: 096d2305fa1d0e05023ec5f79bdeb00b0b296aa0
  React-runtimeexecutor: 1fb11b17da6dcf79da6f301ab54fcb51545bab6e
  React-runtimescheduler: 392b9f1f407d2efdd76d79aaaa381a21d65692bb
  React-utils: ca47876d5d2e394bce2db961782fff82e2c2db88
  ReactCommon: f898adf7b72d82f92e7b63ffa8752bad3bd830c8
  rn-fetch-blob: 7c7f127adb94c9f66db80b325e023ae824ec78a4
  RNCAsyncStorage: 826b603ae9c0f88b5ac4e956801f755109fa4d5c
  RNCClipboard: 0a720adef5ec193aa0e3de24c3977222c7e52a37
  RNCMaskedView: 090213d32d8b3bb83a4dcb7d12c18f0152591906
  RNCPushNotificationIOS: 87b8d16d3ede4532745e05b03c42cff33a36cc45
  RNDeviceInfo: 1e3f62b9ec32f7754fac60bd06b8f8a27124e7f0
  RNFastImage: 2c0b5e588f129839b576669a4e429181ec39c61e
  RNFBAnalytics: 3f336502732e51de9f5a5be8b5e3df879bf2eca7
  RNFBApp: 9acbe359ef3559d6f8ca5c350db471f2b352f98a
  RNFBMessaging: b82ef70d252500d3ad104284c0c7f8e1cd22b51e
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: 8ff45d9b814f3ceceb1e9f92e5c73c92ab98e4a2
  RNPermissions: 34d678157c800b25b22a488e4d8babb57456e796
  RNReactNativeHapticFeedback: 1e3efeca9628ff9876ee7cdd9edec1b336913f8c
  RNScreens: 136d868cc4a78b41042a1a5f1cb42b99a47033f5
  RNSentry: 4bb5f2c72953d0672897b2a54fde7ec6564c80ec
  RNSVG: a48668fd382115bc89761ce291a81c4ca5f2fd2e
  RNVectorIcons: b307b181c8e98d633d416d36421d515cf2fe9e31
  SDWebImage: 2d6d229046fea284d62e36bfb8ebe8287dfc5b10
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  segment-analytics-react-native: 962494a9edbe3f6c5829e3b471484c85c304601c
  Sentry: f8374b5415bc38dfb5645941b3ae31230fbeae57
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  sovran-react-native: e6a9c963a8a6b9ebc3563394c39c30f33ab1453f
  Yoga: 428500217b7b2dba1074041bd3ccf0cad3555925
  ZegoExpressEngine: 68629fb936bc779260ce7d5cb3eea5b467dffa18

PODFILE CHECKSUM: 4d200538ecfa8fedfdd383c4e6a75ffc3784693d

COCOAPODS: 1.15.2
